# Stock Trading AI System

A comprehensive Python-based stock trading AI system that combines technical analysis, machine learning, and risk management for automated trading decisions.

## 🚀 Features

### Core Components

1. **Technical Analysis Engine**
   - Multi-indicator strategy (EMA, RSI, MACD, Bollinger Bands)
   - Custom technical indicators without external dependencies
   - Real-time signal generation

2. **Machine Learning Enhancement**
   - Ensemble methods (Random Forest, XGBoost, Gradient Boosting)
   - Advanced feature engineering (80+ features)
   - Time series cross-validation
   - Feature importance analysis

3. **Risk Management System**
   - Portfolio diversification across sectors
   - Position sizing based on volatility
   - Stop-loss and take-profit mechanisms
   - Sector allocation limits

4. **Comprehensive Backtesting**
   - Backtrader integration
   - Multiple strategy variants testing
   - Performance metrics (Sharpe ratio, max drawdown, win rate)
   - Transaction cost modeling

## 📁 Project Structure

```
RugPlay/
├── src/
│   ├── data/
│   │   └── data_fetcher.py          # Stock data fetching
│   ├── strategies/
│   │   └── multi_indicator_strategy.py  # Technical analysis strategy
│   ├── utils/
│   │   └── technical_indicators.py  # Technical indicators
│   ├── ml_models/
│   │   ├── feature_engineering.py  # ML feature creation
│   │   └── ml_strategy.py          # ML-enhanced strategy
│   ├── risk_management/
│   │   └── portfolio_manager.py    # Risk-managed portfolio
│   ├── backtesting/
│   │   └── backtrader_engine.py    # Backtesting framework
│   └── trading_ai.py               # Main trading AI class
├── tests/
├── docs/
├── results/
├── requirements.txt
├── test_trading_ai.py              # Basic system test
├── comprehensive_backtest.py       # Multi-stock backtesting
├── complete_system_demo.py         # Full system demonstration
└── README.md
```

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd RugPlay
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Optional: Install TA-Lib for additional indicators**
```bash
# On macOS
brew install ta-lib
pip install TA-Lib

# On Ubuntu/Debian
sudo apt-get install libta-lib-dev
pip install TA-Lib
```

## 🚀 Quick Start

### Basic Usage

```python
from src.trading_ai import TradingAI

# Initialize the AI
ai = TradingAI()

# Analyze a single stock
result = ai.analyze_stock('AAPL')
print(ai.generate_report('AAPL'))

# Analyze a portfolio
portfolio_result = ai.analyze_portfolio(['AAPL', 'MSFT', 'GOOGL'])
print(ai.generate_report())
```

### Machine Learning Enhancement

```python
from src.ml_models.ml_strategy import MLTradingStrategy
from src.data.data_fetcher import DataFetcher

# Initialize ML strategy
ml_strategy = MLTradingStrategy(model_type='ensemble')

# Get training data
fetcher = DataFetcher()
data = fetcher.fetch_stock_data('AAPL', period='2y')

# Train models
training_results = ml_strategy.train_models(data)

# Generate predictions
predictions = ml_strategy.predict_signals(data)
```

### Risk-Managed Portfolio

```python
from src.risk_management.portfolio_manager import RiskManagedPortfolio

# Initialize portfolio
portfolio = RiskManagedPortfolio(initial_capital=100000)

# Analyze universe and get recommendations
symbols = ['AAPL', 'MSFT', 'JNJ', 'JPM', 'XOM']
recommendations = portfolio.analyze_universe(symbols)

# Execute trades
execution_results = portfolio.execute_trades(recommendations)

# Generate report
print(portfolio.generate_report())
```

## 🧪 Testing

### Run Basic Tests
```bash
python test_trading_ai.py
```

### Run Comprehensive Backtesting
```bash
python comprehensive_backtest.py
```

### Run Complete System Demo
```bash
python complete_system_demo.py
```

## 📊 Strategy Details

### Technical Analysis Strategy

The multi-indicator strategy combines:

- **EMA Crossover**: 12/26 period EMAs for trend direction
- **RSI**: 14-period RSI for overbought/oversold conditions (30/70 thresholds)
- **MACD**: 12/26/9 MACD for momentum confirmation
- **Bollinger Bands**: 20-period with 2 standard deviations for volatility

**Signal Generation Logic**:
- **BUY**: Bullish EMA trend + RSI oversold + MACD bullish + near lower Bollinger Band
- **SELL**: Bearish EMA trend + RSI overbought + MACD bearish + near upper Bollinger Band
- **HOLD**: Mixed or weak signals

### Machine Learning Enhancement

**Feature Engineering** (80+ features):
- Price-based: ratios, gaps, candlestick patterns
- Volume-based: volume ratios, VWAP, volume momentum
- Volatility: ATR variations, intraday volatility
- Momentum: ROC, price acceleration
- Pattern recognition: trend direction, breakouts
- Market structure: support/resistance, pivot points

**Models**:
- Random Forest Classifier
- XGBoost Classifier  
- Gradient Boosting Classifier
- Ensemble (majority vote)

### Risk Management

**Position Sizing**:
- Maximum 5% of portfolio per position
- Volatility-adjusted position sizing
- 2% maximum portfolio risk per trade

**Diversification**:
- Maximum 25% allocation per sector
- Automatic sector classification
- Geographic diversification support

**Risk Controls**:
- 8% stop-loss on all positions
- 15% take-profit targets
- Portfolio heat monitoring
- Correlation analysis

## 📈 Performance Metrics

The system tracks comprehensive performance metrics:

- **Returns**: Total return, annualized return
- **Risk**: Sharpe ratio, maximum drawdown, volatility
- **Trading**: Win rate, profit factor, average win/loss
- **Portfolio**: Sector allocation, position concentration

## 🔧 Configuration

### Strategy Parameters

```python
strategy_params = {
    'ema_fast': 12,
    'ema_slow': 26,
    'rsi_period': 14,
    'rsi_overbought': 70,
    'rsi_oversold': 30,
    'bb_period': 20,
    'bb_std': 2
}
```

### Risk Management Parameters

```python
risk_params = {
    'max_position_size': 0.05,      # 5% max per position
    'max_sector_allocation': 0.25,   # 25% max per sector
    'stop_loss_pct': 0.08,          # 8% stop loss
    'take_profit_pct': 0.15,        # 15% take profit
    'max_portfolio_risk': 0.02      # 2% max risk per trade
}
```

## 📚 Research Findings

### Technical Analysis Strategies

Based on extensive research, the system implements proven strategies:

1. **Moving Average Systems**: Effective for trend following
2. **Momentum Oscillators**: Good for timing entries/exits
3. **Volatility Indicators**: Help with position sizing
4. **Multi-timeframe Analysis**: Improves signal quality

### Machine Learning Insights

- **Feature Importance**: Price momentum and volatility features most predictive
- **Model Performance**: Ensemble methods outperform individual models
- **Overfitting Prevention**: Time series cross-validation essential
- **Feature Selection**: Reduces noise and improves generalization

### Risk Management Best Practices

- **Diversification**: Reduces portfolio volatility by 20-30%
- **Position Sizing**: Volatility-based sizing improves risk-adjusted returns
- **Stop Losses**: Limit downside while preserving upside potential
- **Sector Limits**: Prevent concentration risk

## 🚨 Disclaimers

⚠️ **Important**: This is an educational project for learning purposes only.

- **Not Financial Advice**: This system is for educational and research purposes
- **Past Performance**: Does not guarantee future results
- **Risk Warning**: Trading involves substantial risk of loss
- **Paper Trading**: Test thoroughly before any real trading
- **Regulatory Compliance**: Ensure compliance with local regulations

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **yfinance**: For stock data access
- **Backtrader**: For backtesting framework
- **scikit-learn**: For machine learning tools
- **pandas/numpy**: For data manipulation
- **matplotlib**: For visualization

## 📞 Support

For questions or issues:

1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed description
4. Include error messages and system information

---

**Happy Trading! 📈**

*Remember: The best strategy is the one you understand and can stick with through market cycles.*
