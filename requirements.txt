# Core data manipulation and analysis
pandas>=2.0.0
numpy>=1.24.0

# Financial data
yfinance>=0.2.18
pandas-datareader>=0.10.0

# Backtesting frameworks
backtrader>=**********
zipline-reloaded>=3.0.0

# Machine learning
scikit-learn>=1.3.0
xgboost>=1.7.0
lightgbm>=4.0.0

# Technical analysis (alternative to TA-Lib)
# ta-lib>=0.4.25  # Requires C library installation
# TA-Lib>=0.4.25  # Requires C library installation

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Statistical analysis
scipy>=1.10.0
statsmodels>=0.14.0

# Optimization
optuna>=3.2.0
hyperopt>=0.2.7

# Utilities
tqdm>=4.65.0
joblib>=1.3.0
python-dotenv>=1.0.0

# Jupyter notebook support
jupyter>=1.0.0
ipykernel>=6.25.0

# Risk management and portfolio optimization
PyPortfolioOpt>=1.5.5
quantlib>=1.31

# Alternative data sources
alpha-vantage>=2.3.1
quandl>=3.7.0
