#!/usr/bin/env python3
"""
Comprehensive backtesting script for the Trading AI.
Tests the strategy on a diverse set of stocks across different sectors.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import json

from src.data.data_fetcher import DataFetcher
from src.backtesting.backtrader_engine import BacktestEngine
from src.trading_ai import TradingAI


class ComprehensiveBacktester:
    """
    Comprehensive backtesting system for evaluating trading strategies across multiple stocks.
    """
    
    def __init__(self, initial_cash: float = 100000):
        """
        Initialize the comprehensive backtester.
        
        Args:
            initial_cash: Starting capital for each backtest
        """
        self.initial_cash = initial_cash
        self.data_fetcher = DataFetcher()
        self.backtest_engine = BacktestEngine(initial_cash)
        self.results = {}
        
        # Define diversified stock portfolio by sector
        self.stock_universe = {
            'Technology': ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'META', 'TSLA', 'NFLX', 'ADBE'],
            'Healthcare': ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'LLY'],
            'Financial': ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK'],
            'Consumer_Discretionary': ['AMZN', 'HD', 'MCD', 'NKE', 'SBUX', 'TGT', 'LOW'],
            'Consumer_Staples': ['PG', 'KO', 'PEP', 'WMT', 'COST', 'CL', 'KMB'],
            'Energy': ['XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO'],
            'Industrials': ['BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'LMT'],
            'Materials': ['LIN', 'APD', 'SHW', 'FCX', 'NEM', 'DOW'],
            'Utilities': ['NEE', 'DUK', 'SO', 'D', 'EXC', 'AEP'],
            'Real_Estate': ['AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'O'],
            'Communication': ['VZ', 'T', 'CMCSA', 'DIS', 'CHTR']
        }
    
    def run_comprehensive_backtest(
        self,
        test_period: str = "2y",
        strategy_variants: list = None
    ) -> dict:
        """
        Run comprehensive backtests across all stocks and strategy variants.
        
        Args:
            test_period: Period for backtesting
            strategy_variants: List of strategy parameter dictionaries to test
        
        Returns:
            Dictionary with comprehensive results
        """
        print("=" * 80)
        print("COMPREHENSIVE BACKTESTING ANALYSIS")
        print("=" * 80)
        
        if strategy_variants is None:
            strategy_variants = self._get_default_strategy_variants()
        
        all_results = {}
        sector_results = {}
        
        # Test each sector
        for sector, symbols in self.stock_universe.items():
            print(f"\nTesting {sector} sector...")
            sector_results[sector] = {}
            
            # Test each stock in the sector
            for symbol in symbols[:3]:  # Limit to 3 stocks per sector for demo
                print(f"  Testing {symbol}...")
                
                try:
                    # Fetch data
                    stock_data = self.data_fetcher.fetch_stock_data(symbol, test_period)
                    
                    # Test each strategy variant
                    symbol_results = {}
                    for i, strategy_params in enumerate(strategy_variants):
                        variant_name = f"variant_{i+1}"
                        
                        # Run backtest
                        result = self.backtest_engine.run_backtest(
                            stock_data, 
                            strategy_params
                        )
                        
                        symbol_results[variant_name] = result
                    
                    sector_results[sector][symbol] = symbol_results
                    all_results[symbol] = symbol_results
                    
                except Exception as e:
                    print(f"    Error testing {symbol}: {e}")
                    continue
        
        # Analyze results
        analysis = self._analyze_results(all_results, sector_results)
        
        # Save results
        self.results = {
            'individual_stocks': all_results,
            'sector_breakdown': sector_results,
            'analysis': analysis,
            'test_parameters': {
                'test_period': test_period,
                'initial_cash': self.initial_cash,
                'strategy_variants': strategy_variants
            },
            'timestamp': datetime.now().isoformat()
        }
        
        return self.results
    
    def _get_default_strategy_variants(self) -> list:
        """Get default strategy variants to test."""
        return [
            # Conservative strategy
            {
                'ema_fast': 20,
                'ema_slow': 50,
                'rsi_overbought': 75,
                'rsi_oversold': 25,
                'risk_per_trade': 0.01,
                'stop_loss_pct': 0.03
            },
            # Aggressive strategy
            {
                'ema_fast': 8,
                'ema_slow': 21,
                'rsi_overbought': 65,
                'rsi_oversold': 35,
                'risk_per_trade': 0.03,
                'stop_loss_pct': 0.07
            },
            # Balanced strategy (default)
            {
                'ema_fast': 12,
                'ema_slow': 26,
                'rsi_overbought': 70,
                'rsi_oversold': 30,
                'risk_per_trade': 0.02,
                'stop_loss_pct': 0.05
            }
        ]
    
    def _analyze_results(self, all_results: dict, sector_results: dict) -> dict:
        """Analyze backtest results and generate insights."""
        analysis = {
            'overall_performance': {},
            'sector_performance': {},
            'strategy_comparison': {},
            'risk_metrics': {},
            'best_performers': {},
            'worst_performers': {}
        }
        
        # Overall performance analysis
        all_returns = []
        all_sharpe_ratios = []
        all_max_drawdowns = []
        all_win_rates = []
        
        for symbol, variants in all_results.items():
            for variant, result in variants.items():
                all_returns.append(result['total_return_pct'])
                all_sharpe_ratios.append(result['sharpe_ratio'])
                all_max_drawdowns.append(result['max_drawdown_pct'])
                all_win_rates.append(result['win_rate'])
        
        analysis['overall_performance'] = {
            'avg_return': np.mean(all_returns),
            'median_return': np.median(all_returns),
            'std_return': np.std(all_returns),
            'avg_sharpe': np.mean(all_sharpe_ratios),
            'avg_max_drawdown': np.mean(all_max_drawdowns),
            'avg_win_rate': np.mean(all_win_rates),
            'positive_returns_pct': len([r for r in all_returns if r > 0]) / len(all_returns) * 100
        }
        
        # Sector performance analysis
        for sector, stocks in sector_results.items():
            sector_returns = []
            for symbol, variants in stocks.items():
                for variant, result in variants.items():
                    sector_returns.append(result['total_return_pct'])
            
            if sector_returns:
                analysis['sector_performance'][sector] = {
                    'avg_return': np.mean(sector_returns),
                    'median_return': np.median(sector_returns),
                    'std_return': np.std(sector_returns),
                    'best_return': max(sector_returns),
                    'worst_return': min(sector_returns)
                }
        
        # Strategy variant comparison
        variant_performance = {'variant_1': [], 'variant_2': [], 'variant_3': []}
        
        for symbol, variants in all_results.items():
            for variant, result in variants.items():
                if variant in variant_performance:
                    variant_performance[variant].append(result['total_return_pct'])
        
        for variant, returns in variant_performance.items():
            if returns:
                analysis['strategy_comparison'][variant] = {
                    'avg_return': np.mean(returns),
                    'std_return': np.std(returns),
                    'win_rate': len([r for r in returns if r > 0]) / len(returns) * 100
                }
        
        # Best and worst performers
        performance_list = []
        for symbol, variants in all_results.items():
            for variant, result in variants.items():
                performance_list.append({
                    'symbol': symbol,
                    'variant': variant,
                    'return': result['total_return_pct'],
                    'sharpe': result['sharpe_ratio'],
                    'max_drawdown': result['max_drawdown_pct']
                })
        
        # Sort by return
        performance_list.sort(key=lambda x: x['return'], reverse=True)
        
        analysis['best_performers'] = performance_list[:10]
        analysis['worst_performers'] = performance_list[-10:]
        
        return analysis
    
    def generate_report(self) -> str:
        """Generate a comprehensive text report."""
        if not self.results:
            return "No backtest results available. Run comprehensive_backtest first."
        
        analysis = self.results['analysis']
        
        report = f"""
COMPREHENSIVE BACKTESTING REPORT
{'=' * 80}

Test Parameters:
- Test Period: {self.results['test_parameters']['test_period']}
- Initial Capital: ${self.results['test_parameters']['initial_cash']:,.0f}
- Strategy Variants: {len(self.results['test_parameters']['strategy_variants'])}
- Total Stocks Tested: {len(self.results['individual_stocks'])}

OVERALL PERFORMANCE SUMMARY
{'-' * 40}
- Average Return: {analysis['overall_performance']['avg_return']:.2f}%
- Median Return: {analysis['overall_performance']['median_return']:.2f}%
- Return Std Dev: {analysis['overall_performance']['std_return']:.2f}%
- Average Sharpe Ratio: {analysis['overall_performance']['avg_sharpe']:.2f}
- Average Max Drawdown: {analysis['overall_performance']['avg_max_drawdown']:.2f}%
- Average Win Rate: {analysis['overall_performance']['avg_win_rate']:.1f}%
- Positive Returns: {analysis['overall_performance']['positive_returns_pct']:.1f}%

SECTOR PERFORMANCE
{'-' * 40}
"""
        
        # Add sector performance
        for sector, metrics in analysis['sector_performance'].items():
            report += f"{sector}: {metrics['avg_return']:.2f}% avg return\n"
        
        report += f"""
STRATEGY VARIANT COMPARISON
{'-' * 40}
"""
        
        # Add strategy comparison
        for variant, metrics in analysis['strategy_comparison'].items():
            report += f"{variant}: {metrics['avg_return']:.2f}% avg return, {metrics['win_rate']:.1f}% win rate\n"
        
        report += f"""
TOP PERFORMERS
{'-' * 40}
"""
        
        # Add top performers
        for i, performer in enumerate(analysis['best_performers'][:5], 1):
            report += f"{i}. {performer['symbol']} ({performer['variant']}): {performer['return']:.2f}%\n"
        
        report += f"""
WORST PERFORMERS
{'-' * 40}
"""
        
        # Add worst performers
        for i, performer in enumerate(analysis['worst_performers'][:5], 1):
            report += f"{i}. {performer['symbol']} ({performer['variant']}): {performer['return']:.2f}%\n"
        
        report += f"\nReport generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return report
    
    def save_results(self, filename: str = None):
        """Save results to JSON file."""
        if filename is None:
            filename = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"Results saved to {filename}")
    
    def create_visualizations(self):
        """Create visualizations of the backtest results."""
        if not self.results:
            print("No results to visualize")
            return
        
        # Extract data for visualization
        returns_data = []
        sectors = []
        variants = []
        
        for symbol, variants_dict in self.results['individual_stocks'].items():
            # Find sector for this symbol
            symbol_sector = None
            for sector, symbols in self.stock_universe.items():
                if symbol in symbols:
                    symbol_sector = sector
                    break
            
            for variant, result in variants_dict.items():
                returns_data.append(result['total_return_pct'])
                sectors.append(symbol_sector or 'Unknown')
                variants.append(variant)
        
        # Create visualizations
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Comprehensive Backtesting Results', fontsize=16)
        
        # 1. Returns distribution
        axes[0, 0].hist(returns_data, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('Distribution of Returns')
        axes[0, 0].set_xlabel('Return (%)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        # 2. Sector performance
        sector_returns = {}
        for sector, returns in zip(sectors, returns_data):
            if sector not in sector_returns:
                sector_returns[sector] = []
            sector_returns[sector].append(returns)
        
        sector_avg_returns = {k: np.mean(v) for k, v in sector_returns.items()}
        
        axes[0, 1].bar(sector_avg_returns.keys(), sector_avg_returns.values())
        axes[0, 1].set_title('Average Returns by Sector')
        axes[0, 1].set_ylabel('Average Return (%)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Strategy variant comparison
        variant_returns = {}
        for variant, returns in zip(variants, returns_data):
            if variant not in variant_returns:
                variant_returns[variant] = []
            variant_returns[variant].append(returns)
        
        variant_data = [variant_returns[k] for k in sorted(variant_returns.keys())]
        axes[1, 0].boxplot(variant_data, labels=sorted(variant_returns.keys()))
        axes[1, 0].set_title('Returns by Strategy Variant')
        axes[1, 0].set_ylabel('Return (%)')
        
        # 4. Risk-Return scatter
        sharpe_ratios = []
        for symbol, variants_dict in self.results['individual_stocks'].items():
            for variant, result in variants_dict.items():
                sharpe_ratios.append(result['sharpe_ratio'])
        
        axes[1, 1].scatter(returns_data, sharpe_ratios, alpha=0.6)
        axes[1, 1].set_title('Risk-Return Profile')
        axes[1, 1].set_xlabel('Return (%)')
        axes[1, 1].set_ylabel('Sharpe Ratio')
        axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.savefig('comprehensive_backtest_results.png', dpi=300, bbox_inches='tight')
        print("Visualizations saved as comprehensive_backtest_results.png")


def main():
    """Main function to run comprehensive backtesting."""
    print("Starting comprehensive backtesting...")
    
    # Initialize backtester
    backtester = ComprehensiveBacktester(initial_cash=100000)
    
    # Run comprehensive backtest
    results = backtester.run_comprehensive_backtest(test_period="1y")
    
    # Generate and print report
    report = backtester.generate_report()
    print(report)
    
    # Save results
    backtester.save_results()
    
    # Create visualizations
    backtester.create_visualizations()
    
    print("\nComprehensive backtesting completed!")


if __name__ == "__main__":
    main()
