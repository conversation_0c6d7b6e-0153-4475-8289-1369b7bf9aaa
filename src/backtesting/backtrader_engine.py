"""
Advanced backtesting engine using Backtrader framework.
Provides comprehensive backtesting capabilities with detailed performance metrics.
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from ..strategies.multi_indicator_strategy import MultiIndicatorStrategy, Signal

logger = logging.getLogger(__name__)


class MultiIndicatorBacktraderStrategy(bt.Strategy):
    """
    Backtrader strategy wrapper for our multi-indicator strategy.
    """
    
    params = (
        ('ema_fast', 12),
        ('ema_slow', 26),
        ('rsi_period', 14),
        ('rsi_overbought', 70),
        ('rsi_oversold', 30),
        ('bb_period', 20),
        ('bb_std', 2),
        ('macd_fast', 12),
        ('macd_slow', 26),
        ('macd_signal', 9),
        ('risk_per_trade', 0.02),  # 2% risk per trade
        ('stop_loss_pct', 0.05),   # 5% stop loss
        ('take_profit_pct', 0.10), # 10% take profit
    )
    
    def __init__(self):
        # Initialize our custom strategy
        self.strategy = MultiIndicatorStrategy(
            ema_fast=self.params.ema_fast,
            ema_slow=self.params.ema_slow,
            rsi_period=self.params.rsi_period,
            rsi_overbought=self.params.rsi_overbought,
            rsi_oversold=self.params.rsi_oversold,
            bb_period=self.params.bb_period,
            bb_std=self.params.bb_std,
            macd_fast=self.params.macd_fast,
            macd_slow=self.params.macd_slow,
            macd_signal=self.params.macd_signal
        )
        
        # Track orders and positions
        self.order = None
        self.entry_price = None
        self.stop_loss_order = None
        self.take_profit_order = None
        
        # Performance tracking
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        
        # Store data for analysis
        self.data_log = []
    
    def next(self):
        """Called for each bar in the data."""
        # Skip if we don't have enough data
        if len(self.data) < max(self.params.ema_slow, self.params.rsi_period, self.params.bb_period):
            return
        
        # Convert current data to DataFrame for our strategy
        current_data = self._get_current_dataframe()
        
        # Calculate indicators and generate signals
        data_with_indicators = self.strategy.calculate_indicators(current_data)
        data_with_signals = self.strategy.generate_signals(data_with_indicators)
        
        # Get the latest signal
        if len(data_with_signals) == 0:
            return
        
        latest_signal = data_with_signals.iloc[-1]['Signal']
        signal_strength = data_with_signals.iloc[-1]['Signal_Strength']
        
        # Log current state
        self.data_log.append({
            'date': self.data.datetime.date(0),
            'close': self.data.close[0],
            'signal': latest_signal,
            'signal_strength': signal_strength,
            'position_size': self.position.size,
            'portfolio_value': self.broker.getvalue()
        })
        
        # Check if we have pending orders
        if self.order:
            return
        
        # Execute trading logic
        if latest_signal == Signal.BUY.value and not self.position:
            self._execute_buy_signal(signal_strength)
        elif latest_signal == Signal.SELL.value and self.position:
            self._execute_sell_signal()
    
    def _get_current_dataframe(self) -> pd.DataFrame:
        """Convert current Backtrader data to DataFrame for our strategy."""
        # Get the last N bars for analysis
        lookback = max(self.params.ema_slow, self.params.rsi_period, self.params.bb_period) + 10
        
        if len(self.data) < lookback:
            lookback = len(self.data)
        
        # Create DataFrame from Backtrader data
        data_dict = {
            'Open': [self.data.open[-i] for i in range(lookback-1, -1, -1)],
            'High': [self.data.high[-i] for i in range(lookback-1, -1, -1)],
            'Low': [self.data.low[-i] for i in range(lookback-1, -1, -1)],
            'Close': [self.data.close[-i] for i in range(lookback-1, -1, -1)],
            'Volume': [self.data.volume[-i] for i in range(lookback-1, -1, -1)]
        }
        
        # Create date index
        dates = [self.data.datetime.date(-i) for i in range(lookback-1, -1, -1)]
        
        df = pd.DataFrame(data_dict, index=pd.DatetimeIndex(dates))
        return df
    
    def _execute_buy_signal(self, signal_strength: float):
        """Execute a buy order with risk management."""
        # Calculate position size based on risk management
        current_price = self.data.close[0]
        stop_loss_price = current_price * (1 - self.params.stop_loss_pct)
        risk_per_share = current_price - stop_loss_price
        
        if risk_per_share <= 0:
            return
        
        # Calculate position size based on risk per trade
        portfolio_value = self.broker.getvalue()
        risk_amount = portfolio_value * self.params.risk_per_trade
        position_size = int(risk_amount / risk_per_share)
        
        # Ensure we don't exceed available cash
        max_shares = int(self.broker.getcash() / current_price)
        position_size = min(position_size, max_shares)
        
        if position_size > 0:
            # Place buy order
            self.order = self.buy(size=position_size)
            self.entry_price = current_price
            
            logger.info(f"BUY signal: {position_size} shares at ${current_price:.2f}")
    
    def _execute_sell_signal(self):
        """Execute a sell order to close position."""
        if self.position.size > 0:
            self.order = self.sell(size=self.position.size)
            logger.info(f"SELL signal: {self.position.size} shares at ${self.data.close[0]:.2f}")
    
    def notify_order(self, order):
        """Called when order status changes."""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                logger.info(f"BUY EXECUTED: {order.executed.size} shares at ${order.executed.price:.2f}")
                
                # Set stop loss and take profit orders
                stop_price = order.executed.price * (1 - self.params.stop_loss_pct)
                profit_price = order.executed.price * (1 + self.params.take_profit_pct)
                
                self.stop_loss_order = self.sell(
                    size=order.executed.size,
                    exectype=bt.Order.Stop,
                    price=stop_price
                )
                
                self.take_profit_order = self.sell(
                    size=order.executed.size,
                    exectype=bt.Order.Limit,
                    price=profit_price
                )
                
            elif order.issell():
                logger.info(f"SELL EXECUTED: {order.executed.size} shares at ${order.executed.price:.2f}")
                
                # Cancel any pending stop loss or take profit orders
                if self.stop_loss_order:
                    self.cancel(self.stop_loss_order)
                    self.stop_loss_order = None
                
                if self.take_profit_order:
                    self.cancel(self.take_profit_order)
                    self.take_profit_order = None
                
                # Track trade performance
                if self.entry_price:
                    pnl = (order.executed.price - self.entry_price) / self.entry_price
                    if pnl > 0:
                        self.win_count += 1
                    else:
                        self.loss_count += 1
                    self.trade_count += 1
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            logger.warning(f"Order {order.status}")
        
        self.order = None
    
    def notify_trade(self, trade):
        """Called when a trade is closed."""
        if trade.isclosed:
            logger.info(f"TRADE CLOSED: PnL ${trade.pnl:.2f}, PnL%: {trade.pnlcomm/trade.value*100:.2f}%")


class BacktestEngine:
    """
    Advanced backtesting engine using Backtrader.
    """
    
    def __init__(self, initial_cash: float = 100000, commission: float = 0.001):
        """
        Initialize the backtest engine.
        
        Args:
            initial_cash: Starting capital
            commission: Commission rate (0.001 = 0.1%)
        """
        self.initial_cash = initial_cash
        self.commission = commission
        self.results = {}
    
    def run_backtest(
        self,
        data: pd.DataFrame,
        strategy_params: Optional[Dict] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict:
        """
        Run a backtest on the provided data.
        
        Args:
            data: DataFrame with OHLCV data
            strategy_params: Parameters for the strategy
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
        
        Returns:
            Dictionary with backtest results
        """
        try:
            # Create Cerebro engine
            cerebro = bt.Cerebro()
            
            # Add strategy with parameters
            if strategy_params:
                cerebro.addstrategy(MultiIndicatorBacktraderStrategy, **strategy_params)
            else:
                cerebro.addstrategy(MultiIndicatorBacktraderStrategy)
            
            # Prepare data
            bt_data = self._prepare_data(data, start_date, end_date)
            cerebro.adddata(bt_data)
            
            # Set broker parameters
            cerebro.broker.setcash(self.initial_cash)
            cerebro.broker.setcommission(commission=self.commission)
            
            # Add analyzers
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
            cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
            
            # Run backtest
            logger.info("Starting backtest...")
            results = cerebro.run()
            
            # Extract results
            strategy_result = results[0]
            final_value = cerebro.broker.getvalue()
            
            # Compile results
            backtest_results = self._compile_results(strategy_result, final_value)
            
            logger.info(f"Backtest completed. Final value: ${final_value:.2f}")
            
            return backtest_results
            
        except Exception as e:
            logger.error(f"Error running backtest: {str(e)}")
            raise
    
    def _prepare_data(self, data: pd.DataFrame, start_date: Optional[str], end_date: Optional[str]) -> bt.feeds.PandasData:
        """Prepare data for Backtrader."""
        # Filter data by date range if specified
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        
        # Ensure required columns exist
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Create Backtrader data feed
        bt_data = bt.feeds.PandasData(
            dataname=data,
            datetime=None,  # Use index as datetime
            open='Open',
            high='High',
            low='Low',
            close='Close',
            volume='Volume',
            openinterest=None
        )
        
        return bt_data
    
    def _compile_results(self, strategy_result, final_value: float) -> Dict:
        """Compile backtest results into a comprehensive dictionary."""
        analyzers = strategy_result.analyzers
        
        # Basic performance metrics
        total_return = (final_value - self.initial_cash) / self.initial_cash
        
        # Sharpe ratio
        sharpe_ratio = analyzers.sharpe.get_analysis().get('sharperatio', 0)
        if sharpe_ratio is None:
            sharpe_ratio = 0
        
        # Drawdown analysis
        drawdown_analysis = analyzers.drawdown.get_analysis()
        max_drawdown = drawdown_analysis.get('max', {}).get('drawdown', 0)
        
        # Trade analysis
        trade_analysis = analyzers.trades.get_analysis()
        total_trades = trade_analysis.get('total', {}).get('total', 0)
        winning_trades = trade_analysis.get('won', {}).get('total', 0)
        losing_trades = trade_analysis.get('lost', {}).get('total', 0)
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Average trade metrics
        avg_win = trade_analysis.get('won', {}).get('pnl', {}).get('average', 0)
        avg_loss = trade_analysis.get('lost', {}).get('pnl', {}).get('average', 0)
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if (avg_loss != 0 and losing_trades > 0) else 0
        
        # SQN (System Quality Number)
        sqn = analyzers.sqn.get_analysis().get('sqn', 0)
        if sqn is None:
            sqn = 0
        
        return {
            'initial_cash': self.initial_cash,
            'final_value': final_value,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown * 100,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'sqn': sqn,
            'data_log': getattr(strategy_result, 'data_log', [])
        }


# Example usage
if __name__ == "__main__":
    print("Backtrader engine module loaded successfully")
    
    # Test with sample data
    engine = BacktestEngine()
    print(f"Backtest engine initialized with ${engine.initial_cash:,.0f} initial cash")
