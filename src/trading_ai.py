"""
Main Trading AI class that orchestrates data fetching, strategy execution, and decision making.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

from .data.data_fetcher import DataFetcher
from .strategies.multi_indicator_strategy import MultiIndicatorStrategy, Signal
from .utils.technical_indicators import TechnicalIndicators

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradingAI:
    """
    Main Trading AI class that combines data fetching, technical analysis, and trading strategies.
    """
    
    def __init__(
        self,
        strategy_params: Optional[Dict] = None,
        data_period: str = "2y",
        data_interval: str = "1d"
    ):
        """
        Initialize the Trading AI.
        
        Args:
            strategy_params: Parameters for the trading strategy
            data_period: Period for historical data
            data_interval: Data interval
        """
        self.data_fetcher = DataFetcher()
        self.strategy = MultiIndicatorStrategy(**(strategy_params or {}))
        self.data_period = data_period
        self.data_interval = data_interval
        
        # Storage for analysis results
        self.analyzed_stocks = {}
        self.portfolio_recommendations = {}
        
        logger.info("Trading AI initialized successfully")
    
    def analyze_stock(self, symbol: str) -> Dict:
        """
        Perform complete analysis of a single stock.
        
        Args:
            symbol: Stock symbol to analyze
        
        Returns:
            Dictionary with analysis results
        """
        try:
            logger.info(f"Analyzing stock: {symbol}")
            
            # Fetch stock data
            stock_data = self.data_fetcher.fetch_stock_data(
                symbol, self.data_period, self.data_interval
            )
            
            # Get stock information
            stock_info = self.data_fetcher.get_stock_info(symbol)
            
            # Calculate technical indicators
            data_with_indicators = self.strategy.calculate_indicators(stock_data)
            
            # Generate trading signals
            data_with_signals = self.strategy.generate_signals(data_with_indicators)
            
            # Get current signal and analysis
            current_analysis = self._get_current_analysis(data_with_signals, stock_info)
            
            # Perform simple backtest
            backtest_results = self.strategy.backtest_strategy(data_with_signals)
            
            # Store results
            analysis_result = {
                'symbol': symbol,
                'stock_info': stock_info,
                'current_analysis': current_analysis,
                'backtest_results': backtest_results,
                'data_with_signals': data_with_signals,
                'last_updated': datetime.now()
            }
            
            self.analyzed_stocks[symbol] = analysis_result
            
            logger.info(f"Analysis completed for {symbol}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {'symbol': symbol, 'error': str(e)}
    
    def analyze_portfolio(self, symbols: List[str]) -> Dict:
        """
        Analyze a portfolio of stocks and provide recommendations.
        
        Args:
            symbols: List of stock symbols to analyze
        
        Returns:
            Dictionary with portfolio analysis and recommendations
        """
        logger.info(f"Analyzing portfolio with {len(symbols)} stocks")
        
        portfolio_analysis = {}
        buy_recommendations = []
        sell_recommendations = []
        hold_recommendations = []
        
        for symbol in symbols:
            try:
                analysis = self.analyze_stock(symbol)
                
                if 'error' not in analysis:
                    portfolio_analysis[symbol] = analysis
                    
                    current_signal = analysis['current_analysis']['current_signal']
                    signal_strength = analysis['current_analysis']['signal_strength']
                    
                    recommendation = {
                        'symbol': symbol,
                        'signal': current_signal,
                        'strength': signal_strength,
                        'reason': analysis['current_analysis']['signal_reason'],
                        'current_price': analysis['current_analysis']['current_price'],
                        'backtest_return': analysis['backtest_results']['total_return']
                    }
                    
                    if current_signal == 'BUY':
                        buy_recommendations.append(recommendation)
                    elif current_signal == 'SELL':
                        sell_recommendations.append(recommendation)
                    else:
                        hold_recommendations.append(recommendation)
                
            except Exception as e:
                logger.warning(f"Failed to analyze {symbol}: {str(e)}")
                continue
        
        # Sort recommendations by signal strength
        buy_recommendations.sort(key=lambda x: x['strength'], reverse=True)
        sell_recommendations.sort(key=lambda x: x['strength'], reverse=True)
        
        portfolio_summary = {
            'total_stocks_analyzed': len(portfolio_analysis),
            'buy_recommendations': buy_recommendations,
            'sell_recommendations': sell_recommendations,
            'hold_recommendations': hold_recommendations,
            'analysis_timestamp': datetime.now(),
            'detailed_analysis': portfolio_analysis
        }
        
        self.portfolio_recommendations = portfolio_summary
        
        logger.info(f"Portfolio analysis completed: {len(buy_recommendations)} BUY, "
                   f"{len(sell_recommendations)} SELL, {len(hold_recommendations)} HOLD")
        
        return portfolio_summary
    
    def _get_current_analysis(self, data_with_signals: pd.DataFrame, stock_info: Dict) -> Dict:
        """
        Extract current analysis from the latest data point.
        
        Args:
            data_with_signals: DataFrame with signals
            stock_info: Stock information
        
        Returns:
            Dictionary with current analysis
        """
        if len(data_with_signals) == 0:
            return {'error': 'No data available'}
        
        latest = data_with_signals.iloc[-1]
        
        # Map signal number to string
        signal_map = {Signal.BUY.value: 'BUY', Signal.SELL.value: 'SELL', Signal.HOLD.value: 'HOLD'}
        current_signal = signal_map.get(latest['Signal'], 'HOLD')
        
        # Calculate additional metrics
        price_change_1d = latest['Close'] - data_with_signals.iloc[-2]['Close'] if len(data_with_signals) > 1 else 0
        price_change_pct_1d = (price_change_1d / data_with_signals.iloc[-2]['Close'] * 100) if len(data_with_signals) > 1 else 0
        
        # Get recent volatility
        recent_volatility = data_with_signals['Close'].pct_change().tail(20).std() * np.sqrt(252) * 100
        
        return {
            'current_signal': current_signal,
            'signal_strength': latest['Signal_Strength'],
            'signal_reason': latest['Signal_Reason'],
            'current_price': latest['Close'],
            'price_change_1d': price_change_1d,
            'price_change_pct_1d': price_change_pct_1d,
            'rsi': latest['RSI'],
            'macd': latest['MACD'],
            'macd_signal': latest['MACD_Signal'],
            'bb_position': latest['BB_Position'],
            'ema_trend': 'BULLISH' if latest['EMA_Fast'] > latest['EMA_Slow'] else 'BEARISH',
            'volatility_20d': recent_volatility,
            'volume_ratio': latest.get('Volume', 0) / data_with_signals['Volume'].tail(20).mean() if 'Volume' in data_with_signals.columns else 1.0
        }
    
    def get_top_recommendations(self, recommendation_type: str = 'BUY', top_n: int = 5) -> List[Dict]:
        """
        Get top N recommendations of a specific type.
        
        Args:
            recommendation_type: 'BUY', 'SELL', or 'HOLD'
            top_n: Number of top recommendations to return
        
        Returns:
            List of top recommendations
        """
        if not self.portfolio_recommendations:
            logger.warning("No portfolio analysis available. Run analyze_portfolio first.")
            return []
        
        key_map = {
            'BUY': 'buy_recommendations',
            'SELL': 'sell_recommendations',
            'HOLD': 'hold_recommendations'
        }
        
        key = key_map.get(recommendation_type.upper())
        if not key:
            logger.error(f"Invalid recommendation type: {recommendation_type}")
            return []
        
        recommendations = self.portfolio_recommendations.get(key, [])
        return recommendations[:top_n]
    
    def generate_report(self, symbol: Optional[str] = None) -> str:
        """
        Generate a text report of the analysis.
        
        Args:
            symbol: Optional symbol for single stock report. If None, generates portfolio report.
        
        Returns:
            Formatted text report
        """
        if symbol:
            return self._generate_stock_report(symbol)
        else:
            return self._generate_portfolio_report()
    
    def _generate_stock_report(self, symbol: str) -> str:
        """Generate a report for a single stock."""
        if symbol not in self.analyzed_stocks:
            return f"No analysis available for {symbol}. Please run analyze_stock('{symbol}') first."
        
        analysis = self.analyzed_stocks[symbol]
        current = analysis['current_analysis']
        backtest = analysis['backtest_results']
        info = analysis['stock_info']
        
        report = f"""
STOCK ANALYSIS REPORT: {symbol}
{'=' * 50}

Company Information:
- Name: {info.get('company_name', 'N/A')}
- Sector: {info.get('sector', 'N/A')}
- Industry: {info.get('industry', 'N/A')}
- Market Cap: ${info.get('market_cap', 0):,.0f}

Current Analysis:
- Signal: {current['current_signal']} (Strength: {current['signal_strength']:.2f})
- Reason: {current['signal_reason']}
- Current Price: ${current['current_price']:.2f}
- 1-Day Change: {current['price_change_pct_1d']:.2f}%
- RSI: {current['rsi']:.1f}
- Trend: {current['ema_trend']}
- Volatility (20d): {current['volatility_20d']:.1f}%

Backtest Results:
- Total Return: {backtest['total_return']:.2f}%
- Total Trades: {backtest['total_trades']}
- Final Value: ${backtest['final_value']:.2f}

Analysis Date: {analysis['last_updated'].strftime('%Y-%m-%d %H:%M:%S')}
"""
        return report
    
    def _generate_portfolio_report(self) -> str:
        """Generate a portfolio summary report."""
        if not self.portfolio_recommendations:
            return "No portfolio analysis available. Please run analyze_portfolio() first."
        
        portfolio = self.portfolio_recommendations
        
        report = f"""
PORTFOLIO ANALYSIS REPORT
{'=' * 50}

Summary:
- Total Stocks Analyzed: {portfolio['total_stocks_analyzed']}
- BUY Signals: {len(portfolio['buy_recommendations'])}
- SELL Signals: {len(portfolio['sell_recommendations'])}
- HOLD Signals: {len(portfolio['hold_recommendations'])}

Top BUY Recommendations:
"""
        
        for i, rec in enumerate(portfolio['buy_recommendations'][:5], 1):
            report += f"{i}. {rec['symbol']} - Strength: {rec['strength']:.2f} - Price: ${rec['current_price']:.2f}\n"
        
        report += "\nTop SELL Recommendations:\n"
        for i, rec in enumerate(portfolio['sell_recommendations'][:5], 1):
            report += f"{i}. {rec['symbol']} - Strength: {rec['strength']:.2f} - Price: ${rec['current_price']:.2f}\n"
        
        report += f"\nAnalysis Date: {portfolio['analysis_timestamp'].strftime('%Y-%m-%d %H:%M:%S')}"
        
        return report


# Example usage
if __name__ == "__main__":
    # Initialize the Trading AI
    ai = TradingAI()
    
    # Test with a single stock
    try:
        result = ai.analyze_stock('AAPL')
        print("Analysis completed for AAPL")
        print(ai.generate_report('AAPL'))
    except Exception as e:
        print(f"Error testing Trading AI: {e}")
