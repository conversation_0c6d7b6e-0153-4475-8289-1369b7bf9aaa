"""
Feature engineering module for machine learning enhanced trading strategies.
Creates features from technical indicators and market data for ML models.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression

from ..utils.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)


class FeatureEngineer:
    """
    Feature engineering class for creating ML-ready features from stock data.
    """
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
        self.scaler = None
        self.feature_selector = None
        self.feature_names = []
    
    def create_features(self, df: pd.DataFrame, target_periods: List[int] = [1, 5, 10]) -> pd.DataFrame:
        """
        Create comprehensive features from OHLCV data.
        
        Args:
            df: DataFrame with OHLCV data
            target_periods: Periods for forward-looking targets
        
        Returns:
            DataFrame with engineered features
        """
        result = df.copy()
        
        # Add all technical indicators
        result = self.indicators.add_all_indicators(result)
        
        # Price-based features
        result = self._add_price_features(result)
        
        # Volume-based features
        result = self._add_volume_features(result)
        
        # Volatility features
        result = self._add_volatility_features(result)
        
        # Momentum features
        result = self._add_momentum_features(result)
        
        # Pattern recognition features
        result = self._add_pattern_features(result)
        
        # Market structure features
        result = self._add_market_structure_features(result)
        
        # Lag features
        result = self._add_lag_features(result)
        
        # Rolling statistics
        result = self._add_rolling_statistics(result)
        
        # Target variables for supervised learning
        result = self._add_target_variables(result, target_periods)
        
        # Store feature names
        self.feature_names = [col for col in result.columns if col not in df.columns]
        
        logger.info(f"Created {len(self.feature_names)} features")
        
        return result
    
    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features."""
        result = df.copy()
        
        # Price ratios
        result['High_Low_Ratio'] = df['High'] / df['Low']
        result['Close_Open_Ratio'] = df['Close'] / df['Open']
        result['High_Close_Ratio'] = df['High'] / df['Close']
        result['Low_Close_Ratio'] = df['Low'] / df['Close']
        
        # Price position within the day's range
        result['Price_Position'] = (df['Close'] - df['Low']) / (df['High'] - df['Low'])
        
        # Gap analysis
        result['Gap'] = df['Open'] - df['Close'].shift(1)
        result['Gap_Pct'] = result['Gap'] / df['Close'].shift(1)
        
        # Body and shadow analysis (candlestick)
        result['Body_Size'] = abs(df['Close'] - df['Open'])
        result['Upper_Shadow'] = df['High'] - np.maximum(df['Open'], df['Close'])
        result['Lower_Shadow'] = np.minimum(df['Open'], df['Close']) - df['Low']
        result['Body_Shadow_Ratio'] = result['Body_Size'] / (result['Upper_Shadow'] + result['Lower_Shadow'] + 1e-8)
        
        return result
    
    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based features."""
        result = df.copy()
        
        # Volume ratios
        result['Volume_SMA_5'] = self.indicators.sma(df['Volume'], 5)
        result['Volume_SMA_20'] = self.indicators.sma(df['Volume'], 20)
        result['Volume_Ratio_5'] = df['Volume'] / result['Volume_SMA_5']
        result['Volume_Ratio_20'] = df['Volume'] / result['Volume_SMA_20']
        
        # Price-volume relationship
        result['Price_Volume'] = df['Close'] * df['Volume']
        result['VWAP'] = result['Price_Volume'].rolling(20).sum() / df['Volume'].rolling(20).sum()
        result['Price_VWAP_Ratio'] = df['Close'] / result['VWAP']
        
        # Volume momentum
        result['Volume_Change'] = df['Volume'].pct_change()
        result['Volume_Momentum_5'] = df['Volume'].rolling(5).mean() / df['Volume'].rolling(10).mean()
        
        return result
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        result = df.copy()
        
        # True Range and ATR variations
        result['ATR_5'] = self.indicators.atr(df['High'], df['Low'], df['Close'], 5)
        result['ATR_14'] = self.indicators.atr(df['High'], df['Low'], df['Close'], 14)
        result['ATR_Ratio'] = result['ATR_5'] / result['ATR_14']
        
        # Price volatility
        result['Price_Volatility_5'] = df['Close'].pct_change().rolling(5).std()
        result['Price_Volatility_20'] = df['Close'].pct_change().rolling(20).std()
        result['Volatility_Ratio'] = result['Price_Volatility_5'] / result['Price_Volatility_20']
        
        # Intraday volatility
        result['Intraday_Volatility'] = (df['High'] - df['Low']) / df['Close']
        result['Intraday_Vol_MA'] = result['Intraday_Volatility'].rolling(10).mean()
        
        return result
    
    def _add_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based features."""
        result = df.copy()
        
        # Rate of Change (ROC)
        for period in [1, 3, 5, 10, 20]:
            result[f'ROC_{period}'] = df['Close'].pct_change(period)
        
        # Momentum oscillators
        result['Momentum_10'] = df['Close'] / df['Close'].shift(10)
        result['Momentum_20'] = df['Close'] / df['Close'].shift(20)
        
        # Price acceleration
        result['Price_Acceleration'] = df['Close'].pct_change().diff()
        
        # Relative performance vs moving averages
        result['Price_vs_SMA20'] = df['Close'] / self.indicators.sma(df['Close'], 20)
        result['Price_vs_SMA50'] = df['Close'] / self.indicators.sma(df['Close'], 50)
        
        return result
    
    def _add_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add pattern recognition features."""
        result = df.copy()
        
        # Trend direction
        result['Trend_5'] = np.where(df['Close'] > df['Close'].shift(5), 1, -1)
        result['Trend_10'] = np.where(df['Close'] > df['Close'].shift(10), 1, -1)
        result['Trend_20'] = np.where(df['Close'] > df['Close'].shift(20), 1, -1)
        
        # Support and resistance levels
        result['Resistance_20'] = df['High'].rolling(20).max()
        result['Support_20'] = df['Low'].rolling(20).min()
        result['Price_vs_Resistance'] = df['Close'] / result['Resistance_20']
        result['Price_vs_Support'] = df['Close'] / result['Support_20']
        
        # Breakout detection
        result['Breakout_High'] = np.where(df['Close'] > result['Resistance_20'].shift(1), 1, 0)
        result['Breakout_Low'] = np.where(df['Close'] < result['Support_20'].shift(1), 1, 0)
        
        # Consecutive up/down days
        result['Up_Days'] = (df['Close'] > df['Close'].shift(1)).astype(int)
        result['Consecutive_Up'] = result['Up_Days'].groupby((result['Up_Days'] != result['Up_Days'].shift()).cumsum()).cumsum()
        result['Consecutive_Down'] = (1 - result['Up_Days']).groupby(((1 - result['Up_Days']) != (1 - result['Up_Days']).shift()).cumsum()).cumsum()
        
        return result
    
    def _add_market_structure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market structure features."""
        result = df.copy()
        
        # Higher highs and lower lows
        result['Higher_High'] = np.where(df['High'] > df['High'].shift(1), 1, 0)
        result['Lower_Low'] = np.where(df['Low'] < df['Low'].shift(1), 1, 0)
        result['Higher_Low'] = np.where(df['Low'] > df['Low'].shift(1), 1, 0)
        result['Lower_High'] = np.where(df['High'] < df['High'].shift(1), 1, 0)
        
        # Market structure score
        result['Bullish_Structure'] = result['Higher_High'] + result['Higher_Low']
        result['Bearish_Structure'] = result['Lower_High'] + result['Lower_Low']
        
        # Pivot points
        result['Pivot'] = (df['High'] + df['Low'] + df['Close']) / 3
        result['R1'] = 2 * result['Pivot'] - df['Low']
        result['S1'] = 2 * result['Pivot'] - df['High']
        result['Price_vs_Pivot'] = df['Close'] / result['Pivot']
        
        return result
    
    def _add_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add lagged features."""
        result = df.copy()
        
        # Lagged returns
        for lag in [1, 2, 3, 5]:
            result[f'Return_Lag_{lag}'] = df['Close'].pct_change().shift(lag)
        
        # Lagged RSI
        rsi = self.indicators.rsi(df['Close'])
        for lag in [1, 2, 3]:
            result[f'RSI_Lag_{lag}'] = rsi.shift(lag)
        
        # Lagged volume
        for lag in [1, 2, 3]:
            result[f'Volume_Lag_{lag}'] = df['Volume'].shift(lag)
        
        return result
    
    def _add_rolling_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add rolling statistical features."""
        result = df.copy()
        
        returns = df['Close'].pct_change()
        
        # Rolling statistics for returns
        for window in [5, 10, 20]:
            result[f'Return_Mean_{window}'] = returns.rolling(window).mean()
            result[f'Return_Std_{window}'] = returns.rolling(window).std()
            result[f'Return_Skew_{window}'] = returns.rolling(window).skew()
            result[f'Return_Kurt_{window}'] = returns.rolling(window).kurt()
        
        # Rolling min/max
        for window in [5, 10, 20]:
            result[f'High_Max_{window}'] = df['High'].rolling(window).max()
            result[f'Low_Min_{window}'] = df['Low'].rolling(window).min()
            result[f'Price_Percentile_{window}'] = df['Close'].rolling(window).rank(pct=True)
        
        return result
    
    def _add_target_variables(self, df: pd.DataFrame, target_periods: List[int]) -> pd.DataFrame:
        """Add target variables for supervised learning."""
        result = df.copy()
        
        for period in target_periods:
            # Future returns
            result[f'Target_Return_{period}d'] = df['Close'].pct_change(period).shift(-period)
            
            # Future direction (binary classification)
            result[f'Target_Direction_{period}d'] = np.where(
                result[f'Target_Return_{period}d'] > 0, 1, 0
            )
            
            # Future volatility
            future_returns = df['Close'].pct_change().rolling(period).std().shift(-period)
            result[f'Target_Volatility_{period}d'] = future_returns
        
        return result
    
    def prepare_ml_data(
        self,
        df: pd.DataFrame,
        target_column: str,
        feature_selection: bool = True,
        n_features: int = 50,
        scale_features: bool = True
    ) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
        """
        Prepare data for machine learning.
        
        Args:
            df: DataFrame with features and targets
            target_column: Name of the target column
            feature_selection: Whether to perform feature selection
            n_features: Number of features to select
            scale_features: Whether to scale features
        
        Returns:
            Tuple of (features_df, target_series, selected_feature_names)
        """
        # Remove rows with NaN values
        clean_df = df.dropna()
        
        if len(clean_df) == 0:
            raise ValueError("No valid data after removing NaN values")
        
        # Separate features and target
        feature_columns = [col for col in clean_df.columns 
                          if not col.startswith('Target_') and 
                          col not in ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']]
        
        X = clean_df[feature_columns]
        y = clean_df[target_column]
        
        # Remove infinite values
        X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Feature selection
        selected_features = feature_columns
        if feature_selection and len(feature_columns) > n_features:
            self.feature_selector = SelectKBest(score_func=f_regression, k=n_features)
            X_selected = self.feature_selector.fit_transform(X, y)
            
            # Get selected feature names
            selected_mask = self.feature_selector.get_support()
            selected_features = [feature_columns[i] for i, selected in enumerate(selected_mask) if selected]
            
            X = pd.DataFrame(X_selected, columns=selected_features, index=X.index)
        
        # Scale features
        if scale_features:
            self.scaler = RobustScaler()
            X_scaled = self.scaler.fit_transform(X)
            X = pd.DataFrame(X_scaled, columns=selected_features, index=X.index)
        
        logger.info(f"Prepared ML data: {X.shape[0]} samples, {X.shape[1]} features")
        
        return X, y, selected_features
    
    def transform_new_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Transform new data using fitted scalers and feature selectors.
        
        Args:
            df: DataFrame with features
        
        Returns:
            Transformed DataFrame
        """
        if self.scaler is None:
            raise ValueError("Scaler not fitted. Call prepare_ml_data first.")
        
        # Apply the same feature engineering
        features_df = self.create_features(df)

        # Remove target columns if they exist
        target_cols = [col for col in features_df.columns if col.startswith('Target_')]
        if target_cols:
            features_df = features_df.drop(columns=target_cols)
        
        # Select the same features
        if hasattr(self, 'feature_names') and self.feature_names:
            available_features = [col for col in self.feature_names if col in features_df.columns]
            features_df = features_df[available_features]
        
        # Handle missing values
        features_df = features_df.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Apply feature selection if fitted
        if self.feature_selector is not None:
            features_df = pd.DataFrame(
                self.feature_selector.transform(features_df),
                columns=features_df.columns[self.feature_selector.get_support()],
                index=features_df.index
            )
        
        # Apply scaling
        features_scaled = self.scaler.transform(features_df)
        features_df = pd.DataFrame(features_scaled, columns=features_df.columns, index=features_df.index)
        
        return features_df


# Example usage
if __name__ == "__main__":
    # Test feature engineering
    engineer = FeatureEngineer()
    
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    sample_data = pd.DataFrame({
        'Open': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'High': 100 + np.cumsum(np.random.randn(100) * 0.5) + np.random.rand(100) * 2,
        'Low': 100 + np.cumsum(np.random.randn(100) * 0.5) - np.random.rand(100) * 2,
        'Close': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'Volume': np.random.randint(1000000, 10000000, 100)
    }, index=dates)
    
    # Test feature creation
    features_df = engineer.create_features(sample_data)
    print(f"Created features: {features_df.shape}")
    print(f"Feature columns: {len(engineer.feature_names)}")
    
    # Test ML data preparation
    try:
        X, y, selected_features = engineer.prepare_ml_data(
            features_df, 
            'Target_Return_5d',
            feature_selection=True,
            n_features=20
        )
        print(f"ML data prepared: X shape {X.shape}, y shape {y.shape}")
        print(f"Selected features: {len(selected_features)}")
    except Exception as e:
        print(f"Error preparing ML data: {e}")
