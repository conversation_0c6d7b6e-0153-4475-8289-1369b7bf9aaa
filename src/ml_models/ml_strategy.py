"""
Machine Learning enhanced trading strategy.
Uses ensemble methods and feature engineering for improved predictions.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, GridSearchCV
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import xgboost as xgb
import joblib
from datetime import datetime

from .feature_engineering import FeatureEngineer
from ..strategies.multi_indicator_strategy import Signal

logger = logging.getLogger(__name__)


class MLTradingStrategy:
    """
    Machine Learning enhanced trading strategy using ensemble methods.
    """
    
    def __init__(
        self,
        model_type: str = 'ensemble',
        prediction_horizon: int = 5,
        min_prediction_confidence: float = 0.6,
        use_feature_selection: bool = True,
        n_features: int = 50
    ):
        """
        Initialize the ML trading strategy.
        
        Args:
            model_type: Type of model ('rf', 'xgb', 'ensemble')
            prediction_horizon: Days ahead to predict
            min_prediction_confidence: Minimum confidence for trading signals
            use_feature_selection: Whether to use feature selection
            n_features: Number of features to select
        """
        self.model_type = model_type
        self.prediction_horizon = prediction_horizon
        self.min_prediction_confidence = min_prediction_confidence
        self.use_feature_selection = use_feature_selection
        self.n_features = n_features
        
        # Initialize components
        self.feature_engineer = FeatureEngineer()
        self.models = {}
        self.is_trained = False
        self.feature_importance = {}
        self.training_metrics = {}
        
        # Model configurations
        self.model_configs = {
            'rf_classifier': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            },
            'rf_regressor': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            },
            'xgb_classifier': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42
            },
            'gb_classifier': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'random_state': 42
            }
        }
    
    def train_models(self, df: pd.DataFrame, validation_split: float = 0.2) -> Dict:
        """
        Train ML models on historical data.
        
        Args:
            df: DataFrame with OHLCV data
            validation_split: Fraction of data to use for validation
        
        Returns:
            Dictionary with training results
        """
        logger.info("Starting ML model training...")
        
        # Create features
        features_df = self.feature_engineer.create_features(df)
        
        # Prepare data for different prediction tasks
        target_column = f'Target_Direction_{self.prediction_horizon}d'
        
        try:
            X, y, selected_features = self.feature_engineer.prepare_ml_data(
                features_df,
                target_column,
                feature_selection=self.use_feature_selection,
                n_features=self.n_features
            )
        except Exception as e:
            logger.error(f"Error preparing ML data: {e}")
            return {'error': str(e)}
        
        # Split data chronologically
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
        
        logger.info(f"Training set: {X_train.shape}, Validation set: {X_val.shape}")
        
        # Train models
        training_results = {}
        
        if self.model_type in ['rf', 'ensemble']:
            training_results['rf'] = self._train_random_forest(X_train, y_train, X_val, y_val)
        
        if self.model_type in ['xgb', 'ensemble']:
            training_results['xgb'] = self._train_xgboost(X_train, y_train, X_val, y_val)
        
        if self.model_type in ['gb', 'ensemble']:
            training_results['gb'] = self._train_gradient_boosting(X_train, y_train, X_val, y_val)
        
        # Train ensemble if specified
        if self.model_type == 'ensemble':
            training_results['ensemble'] = self._create_ensemble_predictions(X_val, y_val)
        
        self.is_trained = True
        self.training_metrics = training_results
        
        logger.info("ML model training completed")
        return training_results
    
    def _train_random_forest(self, X_train, y_train, X_val, y_val) -> Dict:
        """Train Random Forest model."""
        logger.info("Training Random Forest model...")
        
        # Train classifier
        rf_clf = RandomForestClassifier(**self.model_configs['rf_classifier'])
        rf_clf.fit(X_train, y_train)
        
        # Train regressor for confidence scores
        y_train_reg = X_train.index.to_series().apply(lambda x: np.random.random())  # Placeholder
        rf_reg = RandomForestRegressor(**self.model_configs['rf_regressor'])
        rf_reg.fit(X_train, y_train_reg)
        
        # Store models
        self.models['rf_classifier'] = rf_clf
        self.models['rf_regressor'] = rf_reg
        
        # Evaluate
        y_pred = rf_clf.predict(X_val)
        y_pred_proba = rf_clf.predict_proba(X_val)
        
        # Store feature importance
        self.feature_importance['rf'] = dict(zip(X_train.columns, rf_clf.feature_importances_))
        
        return {
            'accuracy': accuracy_score(y_val, y_pred),
            'precision': precision_score(y_val, y_pred, average='weighted'),
            'recall': recall_score(y_val, y_pred, average='weighted'),
            'f1': f1_score(y_val, y_pred, average='weighted'),
            'feature_importance': self.feature_importance['rf']
        }
    
    def _train_xgboost(self, X_train, y_train, X_val, y_val) -> Dict:
        """Train XGBoost model."""
        logger.info("Training XGBoost model...")
        
        # Train classifier
        xgb_clf = xgb.XGBClassifier(**self.model_configs['xgb_classifier'])
        xgb_clf.fit(X_train, y_train)
        
        # Store model
        self.models['xgb_classifier'] = xgb_clf
        
        # Evaluate
        y_pred = xgb_clf.predict(X_val)
        
        # Store feature importance
        self.feature_importance['xgb'] = dict(zip(X_train.columns, xgb_clf.feature_importances_))
        
        return {
            'accuracy': accuracy_score(y_val, y_pred),
            'precision': precision_score(y_val, y_pred, average='weighted'),
            'recall': recall_score(y_val, y_pred, average='weighted'),
            'f1': f1_score(y_val, y_pred, average='weighted'),
            'feature_importance': self.feature_importance['xgb']
        }
    
    def _train_gradient_boosting(self, X_train, y_train, X_val, y_val) -> Dict:
        """Train Gradient Boosting model."""
        logger.info("Training Gradient Boosting model...")
        
        # Train classifier
        gb_clf = GradientBoostingClassifier(**self.model_configs['gb_classifier'])
        gb_clf.fit(X_train, y_train)
        
        # Store model
        self.models['gb_classifier'] = gb_clf
        
        # Evaluate
        y_pred = gb_clf.predict(X_val)
        
        # Store feature importance
        self.feature_importance['gb'] = dict(zip(X_train.columns, gb_clf.feature_importances_))
        
        return {
            'accuracy': accuracy_score(y_val, y_pred),
            'precision': precision_score(y_val, y_pred, average='weighted'),
            'recall': recall_score(y_val, y_pred, average='weighted'),
            'f1': f1_score(y_val, y_pred, average='weighted'),
            'feature_importance': self.feature_importance['gb']
        }
    
    def _create_ensemble_predictions(self, X_val, y_val) -> Dict:
        """Create ensemble predictions from multiple models."""
        logger.info("Creating ensemble predictions...")
        
        predictions = []
        probabilities = []
        
        # Collect predictions from all models
        for model_name, model in self.models.items():
            if 'classifier' in model_name:
                pred = model.predict(X_val)
                pred_proba = model.predict_proba(X_val)
                predictions.append(pred)
                probabilities.append(pred_proba)
        
        if not predictions:
            return {'error': 'No classifier models available for ensemble'}
        
        # Ensemble prediction (majority vote)
        ensemble_pred = np.round(np.mean(predictions, axis=0)).astype(int)
        
        # Ensemble probability (average)
        ensemble_proba = np.mean(probabilities, axis=0)
        
        return {
            'accuracy': accuracy_score(y_val, ensemble_pred),
            'precision': precision_score(y_val, ensemble_pred, average='weighted'),
            'recall': recall_score(y_val, ensemble_pred, average='weighted'),
            'f1': f1_score(y_val, ensemble_pred, average='weighted'),
            'ensemble_method': 'majority_vote'
        }
    
    def predict_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals using ML models.
        
        Args:
            df: DataFrame with OHLCV data
        
        Returns:
            DataFrame with ML-based signals
        """
        if not self.is_trained:
            raise ValueError("Models not trained. Call train_models first.")
        
        # Create features
        features_df = self.feature_engineer.create_features(df)
        
        # Transform features using fitted transformers
        try:
            X = self.feature_engineer.transform_new_data(df)
        except Exception as e:
            logger.error(f"Error transforming features: {e}")
            # Fallback to basic feature preparation
            X = features_df.select_dtypes(include=[np.number]).fillna(0)
        
        result = df.copy()
        
        # Get predictions from all models
        predictions = {}
        confidences = {}
        
        for model_name, model in self.models.items():
            if 'classifier' in model_name:
                try:
                    pred = model.predict(X)
                    pred_proba = model.predict_proba(X)
                    
                    predictions[model_name] = pred
                    confidences[model_name] = np.max(pred_proba, axis=1)
                except Exception as e:
                    logger.warning(f"Error predicting with {model_name}: {e}")
                    continue
        
        if not predictions:
            logger.warning("No valid predictions available")
            result['ML_Signal'] = Signal.HOLD.value
            result['ML_Confidence'] = 0.0
            return result
        
        # Create ensemble predictions
        if len(predictions) > 1:
            # Majority vote
            pred_array = np.array(list(predictions.values()))
            ensemble_pred = np.round(np.mean(pred_array, axis=0)).astype(int)
            
            # Average confidence
            conf_array = np.array(list(confidences.values()))
            ensemble_conf = np.mean(conf_array, axis=0)
        else:
            # Single model
            model_name = list(predictions.keys())[0]
            ensemble_pred = predictions[model_name]
            ensemble_conf = confidences[model_name]
        
        # Convert to trading signals
        ml_signals = []
        ml_confidences = []
        
        for i in range(len(ensemble_pred)):
            pred = ensemble_pred[i]
            conf = ensemble_conf[i]
            
            if conf >= self.min_prediction_confidence:
                if pred == 1:  # Bullish prediction
                    signal = Signal.BUY.value
                else:  # Bearish prediction
                    signal = Signal.SELL.value
            else:
                signal = Signal.HOLD.value
            
            ml_signals.append(signal)
            ml_confidences.append(conf)
        
        result['ML_Signal'] = ml_signals
        result['ML_Confidence'] = ml_confidences
        
        # Add individual model predictions for analysis
        for model_name, pred in predictions.items():
            result[f'{model_name}_pred'] = pred
            result[f'{model_name}_conf'] = confidences[model_name]
        
        return result
    
    def get_feature_importance(self, top_n: int = 20) -> Dict:
        """
        Get feature importance from trained models.
        
        Args:
            top_n: Number of top features to return
        
        Returns:
            Dictionary with feature importance for each model
        """
        if not self.feature_importance:
            return {}
        
        result = {}
        for model_name, importance_dict in self.feature_importance.items():
            # Sort by importance
            sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
            result[model_name] = dict(sorted_features[:top_n])
        
        return result
    
    def save_models(self, filepath: str):
        """Save trained models to file."""
        if not self.is_trained:
            raise ValueError("No trained models to save")
        
        model_data = {
            'models': self.models,
            'feature_engineer': self.feature_engineer,
            'feature_importance': self.feature_importance,
            'training_metrics': self.training_metrics,
            'config': {
                'model_type': self.model_type,
                'prediction_horizon': self.prediction_horizon,
                'min_prediction_confidence': self.min_prediction_confidence,
                'use_feature_selection': self.use_feature_selection,
                'n_features': self.n_features
            }
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"Models saved to {filepath}")
    
    def load_models(self, filepath: str):
        """Load trained models from file."""
        model_data = joblib.load(filepath)
        
        self.models = model_data['models']
        self.feature_engineer = model_data['feature_engineer']
        self.feature_importance = model_data['feature_importance']
        self.training_metrics = model_data['training_metrics']
        
        # Update config
        config = model_data['config']
        self.model_type = config['model_type']
        self.prediction_horizon = config['prediction_horizon']
        self.min_prediction_confidence = config['min_prediction_confidence']
        self.use_feature_selection = config['use_feature_selection']
        self.n_features = config['n_features']
        
        self.is_trained = True
        logger.info(f"Models loaded from {filepath}")


# Example usage
if __name__ == "__main__":
    print("ML Trading Strategy module loaded successfully")
    
    # Test initialization
    ml_strategy = MLTradingStrategy(model_type='ensemble')
    print(f"ML strategy initialized: {ml_strategy.model_type} model")
    print(f"Prediction horizon: {ml_strategy.prediction_horizon} days")
    print(f"Min confidence: {ml_strategy.min_prediction_confidence}")
