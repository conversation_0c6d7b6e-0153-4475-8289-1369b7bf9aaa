"""
Multi-indicator trading strategy implementation.
Combines multiple technical indicators for buy/sell/hold decisions.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from enum import Enum

from ..utils.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)


class Signal(Enum):
    """Trading signals."""
    BUY = 1
    SELL = -1
    HOLD = 0


class MultiIndicatorStrategy:
    """
    Multi-indicator trading strategy that combines:
    - EMA crossover for trend direction
    - RSI for overbought/oversold conditions
    - MACD for momentum confirmation
    - Bollinger Bands for volatility assessment
    """
    
    def __init__(
        self,
        ema_fast: int = 12,
        ema_slow: int = 26,
        rsi_period: int = 14,
        rsi_overbought: float = 70,
        rsi_oversold: float = 30,
        bb_period: int = 20,
        bb_std: float = 2,
        macd_fast: int = 12,
        macd_slow: int = 26,
        macd_signal: int = 9
    ):
        """
        Initialize the strategy with parameters.
        
        Args:
            ema_fast: Fast EMA period
            ema_slow: Slow EMA period
            rsi_period: RSI calculation period
            rsi_overbought: RSI overbought threshold
            rsi_oversold: RSI oversold threshold
            bb_period: Bollinger Bands period
            bb_std: Bollinger Bands standard deviation multiplier
            macd_fast: MACD fast EMA period
            macd_slow: MACD slow EMA period
            macd_signal: MACD signal line period
        """
        self.ema_fast = ema_fast
        self.ema_slow = ema_slow
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.bb_period = bb_period
        self.bb_std = bb_std
        self.macd_fast = macd_fast
        self.macd_slow = macd_slow
        self.macd_signal = macd_signal
        
        self.indicators = TechnicalIndicators()
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all required technical indicators.
        
        Args:
            df: DataFrame with OHLCV data
        
        Returns:
            DataFrame with indicators added
        """
        result = df.copy()
        
        # EMAs for trend
        result['EMA_Fast'] = self.indicators.ema(df['Close'], self.ema_fast)
        result['EMA_Slow'] = self.indicators.ema(df['Close'], self.ema_slow)
        
        # RSI for momentum
        result['RSI'] = self.indicators.rsi(df['Close'], self.rsi_period)
        
        # MACD for momentum confirmation
        macd_line, signal_line, histogram = self.indicators.macd(
            df['Close'], self.macd_fast, self.macd_slow, self.macd_signal
        )
        result['MACD'] = macd_line
        result['MACD_Signal'] = signal_line
        result['MACD_Histogram'] = histogram
        
        # Bollinger Bands for volatility
        bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
            df['Close'], self.bb_period, self.bb_std
        )
        result['BB_Upper'] = bb_upper
        result['BB_Middle'] = bb_middle
        result['BB_Lower'] = bb_lower
        result['BB_Position'] = (df['Close'] - bb_lower) / (bb_upper - bb_lower)
        
        return result
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on the strategy rules.
        
        Args:
            df: DataFrame with OHLCV data and indicators
        
        Returns:
            DataFrame with signals added
        """
        result = df.copy()
        
        # Initialize signal column
        result['Signal'] = Signal.HOLD.value
        result['Signal_Strength'] = 0.0
        result['Signal_Reason'] = ''
        
        # Calculate individual signal components
        result = self._calculate_trend_signals(result)
        result = self._calculate_momentum_signals(result)
        result = self._calculate_volatility_signals(result)
        
        # Combine signals using weighted approach
        result = self._combine_signals(result)
        
        return result
    
    def _calculate_trend_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate trend-based signals using EMA crossover."""
        result = df.copy()
        
        # EMA crossover signals
        result['EMA_Bullish'] = (result['EMA_Fast'] > result['EMA_Slow']).astype(int)
        result['EMA_Crossover'] = result['EMA_Bullish'].diff()
        
        return result
    
    def _calculate_momentum_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate momentum-based signals using RSI and MACD."""
        result = df.copy()
        
        # RSI signals
        result['RSI_Oversold'] = (result['RSI'] < self.rsi_oversold).astype(int)
        result['RSI_Overbought'] = (result['RSI'] > self.rsi_overbought).astype(int)
        
        # MACD signals
        result['MACD_Bullish'] = (result['MACD'] > result['MACD_Signal']).astype(int)
        result['MACD_Crossover'] = result['MACD_Bullish'].diff()
        
        return result
    
    def _calculate_volatility_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate volatility-based signals using Bollinger Bands."""
        result = df.copy()
        
        # Bollinger Band signals
        result['BB_Lower_Touch'] = (result['Close'] <= result['BB_Lower']).astype(int)
        result['BB_Upper_Touch'] = (result['Close'] >= result['BB_Upper']).astype(int)
        result['BB_Squeeze'] = ((result['BB_Upper'] - result['BB_Lower']) / result['BB_Middle'] < 0.1).astype(int)
        
        return result
    
    def _combine_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Combine individual signals into final trading signals."""
        result = df.copy()
        
        # Define signal weights
        weights = {
            'trend': 0.4,
            'momentum': 0.4,
            'volatility': 0.2
        }
        
        for i in range(len(result)):
            if i < max(self.ema_slow, self.rsi_period, self.bb_period):
                # Not enough data for reliable signals
                continue
            
            signal_score = 0.0
            reasons = []
            
            # Trend component (EMA crossover)
            if result.iloc[i]['EMA_Crossover'] == 1:  # Bullish crossover
                signal_score += weights['trend']
                reasons.append('EMA_Bullish_Cross')
            elif result.iloc[i]['EMA_Crossover'] == -1:  # Bearish crossover
                signal_score -= weights['trend']
                reasons.append('EMA_Bearish_Cross')
            elif result.iloc[i]['EMA_Bullish'] == 1:  # Trend continuation
                signal_score += weights['trend'] * 0.5
                reasons.append('EMA_Bullish_Trend')
            else:
                signal_score -= weights['trend'] * 0.5
                reasons.append('EMA_Bearish_Trend')
            
            # Momentum component (RSI + MACD)
            momentum_score = 0.0
            
            # RSI component
            if result.iloc[i]['RSI_Oversold'] == 1:
                momentum_score += 0.5
                reasons.append('RSI_Oversold')
            elif result.iloc[i]['RSI_Overbought'] == 1:
                momentum_score -= 0.5
                reasons.append('RSI_Overbought')
            
            # MACD component
            if result.iloc[i]['MACD_Crossover'] == 1:
                momentum_score += 0.5
                reasons.append('MACD_Bullish_Cross')
            elif result.iloc[i]['MACD_Crossover'] == -1:
                momentum_score -= 0.5
                reasons.append('MACD_Bearish_Cross')
            elif result.iloc[i]['MACD_Bullish'] == 1:
                momentum_score += 0.25
                reasons.append('MACD_Bullish')
            else:
                momentum_score -= 0.25
                reasons.append('MACD_Bearish')
            
            signal_score += weights['momentum'] * momentum_score
            
            # Volatility component (Bollinger Bands)
            if result.iloc[i]['BB_Lower_Touch'] == 1:
                signal_score += weights['volatility']
                reasons.append('BB_Lower_Touch')
            elif result.iloc[i]['BB_Upper_Touch'] == 1:
                signal_score -= weights['volatility']
                reasons.append('BB_Upper_Touch')
            
            # Determine final signal
            if signal_score > 0.3:
                result.iloc[i, result.columns.get_loc('Signal')] = Signal.BUY.value
            elif signal_score < -0.3:
                result.iloc[i, result.columns.get_loc('Signal')] = Signal.SELL.value
            else:
                result.iloc[i, result.columns.get_loc('Signal')] = Signal.HOLD.value
            
            result.iloc[i, result.columns.get_loc('Signal_Strength')] = abs(signal_score)
            result.iloc[i, result.columns.get_loc('Signal_Reason')] = ', '.join(reasons)
        
        return result
    
    def backtest_strategy(self, df: pd.DataFrame, initial_capital: float = 10000) -> Dict:
        """
        Simple backtest of the strategy.
        
        Args:
            df: DataFrame with signals
            initial_capital: Starting capital
        
        Returns:
            Dictionary with backtest results
        """
        capital = initial_capital
        position = 0  # 0 = no position, 1 = long, -1 = short
        trades = []
        portfolio_values = []
        
        for i in range(len(df)):
            current_price = df.iloc[i]['Close']
            signal = df.iloc[i]['Signal']
            date = df.index[i]
            
            # Calculate current portfolio value
            if position == 0:
                portfolio_value = capital
            else:
                portfolio_value = capital + (position * (current_price - entry_price) * shares)
            
            portfolio_values.append(portfolio_value)
            
            # Execute trades based on signals
            if signal == Signal.BUY.value and position <= 0:
                if position == -1:  # Close short position
                    capital += (entry_price - current_price) * shares
                
                # Open long position
                shares = capital // current_price
                entry_price = current_price
                position = 1
                
                trades.append({
                    'date': date,
                    'action': 'BUY',
                    'price': current_price,
                    'shares': shares,
                    'capital': capital
                })
                
            elif signal == Signal.SELL.value and position >= 0:
                if position == 1:  # Close long position
                    capital = shares * current_price
                
                # For simplicity, we'll just close positions rather than short
                position = 0
                
                trades.append({
                    'date': date,
                    'action': 'SELL',
                    'price': current_price,
                    'shares': shares if 'shares' in locals() else 0,
                    'capital': capital
                })
        
        # Calculate final portfolio value
        if position == 1 and 'shares' in locals():
            final_value = shares * df.iloc[-1]['Close']
        else:
            final_value = capital
        
        # Calculate performance metrics
        total_return = (final_value - initial_capital) / initial_capital
        portfolio_series = pd.Series(portfolio_values, index=df.index)
        
        return {
            'initial_capital': initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'total_trades': len(trades),
            'trades': trades,
            'portfolio_values': portfolio_series
        }


# Example usage
if __name__ == "__main__":
    # This would typically be called with real data
    print("Multi-indicator strategy module loaded successfully")
    
    # Create a simple test
    strategy = MultiIndicatorStrategy()
    print(f"Strategy initialized with EMA periods: {strategy.ema_fast}, {strategy.ema_slow}")
    print(f"RSI thresholds: {strategy.rsi_oversold}, {strategy.rsi_overbought}")
