"""
Risk-managed portfolio trading system.
Focuses on minimizing losses and managing risk through diversification and position sizing.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..data.data_fetcher import DataFetcher
from ..strategies.multi_indicator_strategy import MultiIndicatorStrategy, Signal
from ..ml_models.ml_strategy import MLTradingStrategy

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """Represents a trading position."""
    symbol: str
    shares: int
    entry_price: float
    entry_date: datetime
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    sector: str


@dataclass
class RiskMetrics:
    """Risk metrics for the portfolio."""
    total_value: float
    total_risk: float
    max_position_risk: float
    sector_concentration: Dict[str, float]
    var_95: float  # Value at Risk at 95% confidence
    sharpe_ratio: float
    max_drawdown: float


class RiskManagedPortfolio:
    """
    Risk-managed portfolio that focuses on minimizing losses and managing risk.
    """
    
    def __init__(
        self,
        initial_capital: float = 100000,
        max_position_size: float = 0.05,  # 5% max per position
        max_sector_allocation: float = 0.25,  # 25% max per sector
        stop_loss_pct: float = 0.08,  # 8% stop loss
        take_profit_pct: float = 0.15,  # 15% take profit
        max_portfolio_risk: float = 0.02,  # 2% max portfolio risk per trade
        rebalance_threshold: float = 0.05  # 5% deviation triggers rebalance
    ):
        """
        Initialize the risk-managed portfolio.
        
        Args:
            initial_capital: Starting capital
            max_position_size: Maximum position size as fraction of portfolio
            max_sector_allocation: Maximum allocation per sector
            stop_loss_pct: Stop loss percentage
            take_profit_pct: Take profit percentage
            max_portfolio_risk: Maximum portfolio risk per trade
            rebalance_threshold: Threshold for rebalancing
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_position_size = max_position_size
        self.max_sector_allocation = max_sector_allocation
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.max_portfolio_risk = max_portfolio_risk
        self.rebalance_threshold = rebalance_threshold
        
        # Portfolio state
        self.positions: Dict[str, Position] = {}
        self.cash = initial_capital
        self.portfolio_history = []
        
        # Components
        self.data_fetcher = DataFetcher()
        self.strategy = MultiIndicatorStrategy()
        self.ml_strategy = None  # Optional ML enhancement
        
        # Sector definitions for diversification
        self.sector_map = {
            # Technology
            'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology', 
            'NVDA': 'Technology', 'META': 'Technology', 'TSLA': 'Technology',
            'NFLX': 'Technology', 'ADBE': 'Technology',
            
            # Healthcare
            'JNJ': 'Healthcare', 'PFE': 'Healthcare', 'UNH': 'Healthcare',
            'ABBV': 'Healthcare', 'MRK': 'Healthcare', 'TMO': 'Healthcare',
            
            # Financial
            'JPM': 'Financial', 'BAC': 'Financial', 'WFC': 'Financial',
            'GS': 'Financial', 'MS': 'Financial', 'C': 'Financial',
            
            # Consumer Discretionary
            'AMZN': 'Consumer_Discretionary', 'HD': 'Consumer_Discretionary',
            'MCD': 'Consumer_Discretionary', 'NKE': 'Consumer_Discretionary',
            
            # Consumer Staples
            'PG': 'Consumer_Staples', 'KO': 'Consumer_Staples',
            'PEP': 'Consumer_Staples', 'WMT': 'Consumer_Staples',
            
            # Energy
            'XOM': 'Energy', 'CVX': 'Energy', 'COP': 'Energy',
            
            # Industrials
            'BA': 'Industrials', 'CAT': 'Industrials', 'GE': 'Industrials',
            
            # Materials
            'LIN': 'Materials', 'APD': 'Materials',
            
            # Utilities
            'NEE': 'Utilities', 'DUK': 'Utilities',
            
            # Real Estate
            'AMT': 'Real_Estate', 'PLD': 'Real_Estate',
            
            # Communication
            'VZ': 'Communication', 'T': 'Communication', 'DIS': 'Communication'
        }
        
        logger.info("Risk-managed portfolio initialized")
    
    def add_ml_strategy(self, ml_strategy: MLTradingStrategy):
        """Add ML strategy for enhanced decision making."""
        self.ml_strategy = ml_strategy
        logger.info("ML strategy added to portfolio")
    
    def analyze_universe(self, symbols: List[str]) -> Dict:
        """
        Analyze a universe of stocks and generate recommendations.
        
        Args:
            symbols: List of stock symbols to analyze
        
        Returns:
            Dictionary with analysis and recommendations
        """
        logger.info(f"Analyzing universe of {len(symbols)} stocks")
        
        recommendations = {
            'buy_candidates': [],
            'sell_candidates': [],
            'hold_positions': [],
            'risk_analysis': {}
        }
        
        for symbol in symbols:
            try:
                # Get stock data
                stock_data = self.data_fetcher.fetch_stock_data(symbol, period='1y')
                stock_info = self.data_fetcher.get_stock_info(symbol)
                
                # Technical analysis
                data_with_indicators = self.strategy.calculate_indicators(stock_data)
                data_with_signals = self.strategy.generate_signals(data_with_indicators)
                
                # ML analysis (if available)
                ml_signal = None
                ml_confidence = 0.0
                if self.ml_strategy and self.ml_strategy.is_trained:
                    try:
                        ml_data = self.ml_strategy.predict_signals(stock_data)
                        ml_signal = ml_data['ML_Signal'].iloc[-1]
                        ml_confidence = ml_data['ML_Confidence'].iloc[-1]
                    except Exception as e:
                        logger.warning(f"ML prediction failed for {symbol}: {e}")
                
                # Get latest signals
                latest_signal = data_with_signals['Signal'].iloc[-1]
                signal_strength = data_with_signals['Signal_Strength'].iloc[-1]
                current_price = stock_data['Close'].iloc[-1]
                
                # Risk assessment
                volatility = stock_data['Close'].pct_change().tail(20).std() * np.sqrt(252)
                sector = self.sector_map.get(symbol, 'Unknown')
                
                # Position sizing based on volatility
                position_risk = min(self.max_portfolio_risk / volatility, self.max_position_size)
                
                analysis = {
                    'symbol': symbol,
                    'sector': sector,
                    'current_price': current_price,
                    'technical_signal': latest_signal,
                    'signal_strength': signal_strength,
                    'ml_signal': ml_signal,
                    'ml_confidence': ml_confidence,
                    'volatility': volatility,
                    'position_risk': position_risk,
                    'market_cap': stock_info.get('market_cap', 0),
                    'pe_ratio': stock_info.get('pe_ratio', 0)
                }
                
                # Categorize recommendations
                if self._should_buy(analysis):
                    recommendations['buy_candidates'].append(analysis)
                elif self._should_sell(analysis):
                    recommendations['sell_candidates'].append(analysis)
                else:
                    recommendations['hold_positions'].append(analysis)
                
            except Exception as e:
                logger.warning(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort recommendations by attractiveness
        recommendations['buy_candidates'].sort(
            key=lambda x: x['signal_strength'] * (1 + x['ml_confidence']), 
            reverse=True
        )
        recommendations['sell_candidates'].sort(
            key=lambda x: x['signal_strength'] * (1 + x['ml_confidence']), 
            reverse=True
        )
        
        # Risk analysis
        recommendations['risk_analysis'] = self._analyze_portfolio_risk(recommendations)
        
        return recommendations
    
    def _should_buy(self, analysis: Dict) -> bool:
        """Determine if a stock should be bought based on analysis."""
        # Technical signal
        technical_buy = analysis['technical_signal'] == Signal.BUY.value
        
        # ML signal (if available)
        ml_buy = (analysis['ml_signal'] == Signal.BUY.value and 
                 analysis['ml_confidence'] > 0.6) if analysis['ml_signal'] is not None else True
        
        # Risk constraints
        volatility_ok = analysis['volatility'] < 0.4  # Max 40% annual volatility
        
        # Sector constraints
        sector_ok = self._check_sector_allocation(analysis['sector'])
        
        return technical_buy and ml_buy and volatility_ok and sector_ok
    
    def _should_sell(self, analysis: Dict) -> bool:
        """Determine if a position should be sold."""
        # Check if we have a position
        if analysis['symbol'] not in self.positions:
            return False
        
        position = self.positions[analysis['symbol']]
        
        # Stop loss
        if position.unrealized_pnl_pct <= -self.stop_loss_pct:
            return True
        
        # Take profit
        if position.unrealized_pnl_pct >= self.take_profit_pct:
            return True
        
        # Technical signal
        technical_sell = analysis['technical_signal'] == Signal.SELL.value
        
        # ML signal
        ml_sell = (analysis['ml_signal'] == Signal.SELL.value and 
                  analysis['ml_confidence'] > 0.6) if analysis['ml_signal'] is not None else False
        
        return technical_sell or ml_sell
    
    def _check_sector_allocation(self, sector: str) -> bool:
        """Check if adding to a sector would exceed allocation limits."""
        current_allocation = self._get_sector_allocation()
        current_sector_pct = current_allocation.get(sector, 0)
        
        return current_sector_pct < self.max_sector_allocation
    
    def _get_sector_allocation(self) -> Dict[str, float]:
        """Get current sector allocation percentages."""
        total_value = self._get_portfolio_value()
        sector_values = {}
        
        for position in self.positions.values():
            sector = position.sector
            position_value = position.shares * position.current_price
            sector_values[sector] = sector_values.get(sector, 0) + position_value
        
        # Convert to percentages
        sector_allocation = {}
        for sector, value in sector_values.items():
            sector_allocation[sector] = value / total_value if total_value > 0 else 0
        
        return sector_allocation
    
    def _analyze_portfolio_risk(self, recommendations: Dict) -> Dict:
        """Analyze overall portfolio risk."""
        total_value = self._get_portfolio_value()
        
        # Calculate sector concentrations
        sector_allocation = self._get_sector_allocation()
        
        # Calculate portfolio volatility (simplified)
        portfolio_volatility = 0.0
        if self.positions:
            position_volatilities = []
            position_weights = []
            
            for position in self.positions.values():
                try:
                    # Get recent data for volatility calculation
                    data = self.data_fetcher.fetch_stock_data(position.symbol, period='3mo')
                    vol = data['Close'].pct_change().std() * np.sqrt(252)
                    weight = (position.shares * position.current_price) / total_value
                    
                    position_volatilities.append(vol)
                    position_weights.append(weight)
                except:
                    continue
            
            if position_volatilities:
                # Simplified portfolio volatility (assumes zero correlation)
                portfolio_volatility = np.sqrt(sum(w**2 * v**2 for w, v in zip(position_weights, position_volatilities)))
        
        # Value at Risk (simplified)
        var_95 = total_value * portfolio_volatility * 1.645  # 95% confidence, 1-day VaR
        
        return {
            'total_portfolio_value': total_value,
            'cash_percentage': self.cash / total_value * 100 if total_value > 0 else 100,
            'sector_allocation': sector_allocation,
            'portfolio_volatility': portfolio_volatility,
            'var_95_1day': var_95,
            'max_sector_concentration': max(sector_allocation.values()) if sector_allocation else 0,
            'number_of_positions': len(self.positions)
        }
    
    def execute_trades(self, recommendations: Dict) -> Dict:
        """
        Execute trades based on recommendations.
        
        Args:
            recommendations: Dictionary with buy/sell recommendations
        
        Returns:
            Dictionary with execution results
        """
        execution_results = {
            'executed_buys': [],
            'executed_sells': [],
            'rejected_trades': [],
            'portfolio_summary': {}
        }
        
        # Execute sells first to free up capital
        for sell_candidate in recommendations['sell_candidates']:
            if sell_candidate['symbol'] in self.positions:
                result = self._execute_sell(sell_candidate['symbol'])
                if result['success']:
                    execution_results['executed_sells'].append(result)
                else:
                    execution_results['rejected_trades'].append(result)
        
        # Execute buys
        for buy_candidate in recommendations['buy_candidates']:
            if buy_candidate['symbol'] not in self.positions:
                result = self._execute_buy(buy_candidate)
                if result['success']:
                    execution_results['executed_buys'].append(result)
                else:
                    execution_results['rejected_trades'].append(result)
        
        # Update portfolio summary
        execution_results['portfolio_summary'] = self._get_portfolio_summary()
        
        return execution_results
    
    def _execute_buy(self, analysis: Dict) -> Dict:
        """Execute a buy order."""
        symbol = analysis['symbol']
        current_price = analysis['current_price']
        sector = analysis['sector']
        
        # Calculate position size
        portfolio_value = self._get_portfolio_value()
        max_position_value = portfolio_value * analysis['position_risk']
        shares = int(max_position_value / current_price)
        
        # Check if we have enough cash
        required_cash = shares * current_price
        if required_cash > self.cash:
            shares = int(self.cash / current_price)
            required_cash = shares * current_price
        
        if shares == 0:
            return {'success': False, 'reason': 'Insufficient cash', 'symbol': symbol}
        
        # Execute trade
        self.cash -= required_cash
        
        position = Position(
            symbol=symbol,
            shares=shares,
            entry_price=current_price,
            entry_date=datetime.now(),
            current_price=current_price,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            sector=sector
        )
        
        self.positions[symbol] = position
        
        logger.info(f"BUY: {shares} shares of {symbol} at ${current_price:.2f}")
        
        return {
            'success': True,
            'action': 'BUY',
            'symbol': symbol,
            'shares': shares,
            'price': current_price,
            'total_cost': required_cash
        }
    
    def _execute_sell(self, symbol: str) -> Dict:
        """Execute a sell order."""
        if symbol not in self.positions:
            return {'success': False, 'reason': 'No position found', 'symbol': symbol}
        
        position = self.positions[symbol]
        
        # Get current price
        try:
            current_data = self.data_fetcher.fetch_stock_data(symbol, period='1d')
            current_price = current_data['Close'].iloc[-1]
        except:
            current_price = position.current_price
        
        # Execute trade
        proceeds = position.shares * current_price
        self.cash += proceeds
        
        # Calculate P&L
        pnl = proceeds - (position.shares * position.entry_price)
        pnl_pct = pnl / (position.shares * position.entry_price) * 100
        
        # Remove position
        del self.positions[symbol]
        
        logger.info(f"SELL: {position.shares} shares of {symbol} at ${current_price:.2f}, P&L: ${pnl:.2f} ({pnl_pct:.2f}%)")
        
        return {
            'success': True,
            'action': 'SELL',
            'symbol': symbol,
            'shares': position.shares,
            'price': current_price,
            'proceeds': proceeds,
            'pnl': pnl,
            'pnl_pct': pnl_pct
        }
    
    def _get_portfolio_value(self) -> float:
        """Calculate total portfolio value."""
        total_value = self.cash
        
        for position in self.positions.values():
            total_value += position.shares * position.current_price
        
        return total_value
    
    def _get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary."""
        total_value = self._get_portfolio_value()
        total_return = (total_value - self.initial_capital) / self.initial_capital * 100
        
        return {
            'total_value': total_value,
            'cash': self.cash,
            'invested_value': total_value - self.cash,
            'total_return_pct': total_return,
            'number_of_positions': len(self.positions),
            'sector_allocation': self._get_sector_allocation(),
            'largest_position': max([p.shares * p.current_price for p in self.positions.values()]) if self.positions else 0
        }
    
    def update_positions(self):
        """Update current prices and P&L for all positions."""
        for symbol, position in self.positions.items():
            try:
                current_data = self.data_fetcher.fetch_stock_data(symbol, period='1d')
                current_price = current_data['Close'].iloc[-1]
                
                position.current_price = current_price
                position.unrealized_pnl = (current_price - position.entry_price) * position.shares
                position.unrealized_pnl_pct = (current_price - position.entry_price) / position.entry_price * 100
                
            except Exception as e:
                logger.warning(f"Error updating position for {symbol}: {e}")
    
    def generate_report(self) -> str:
        """Generate a comprehensive portfolio report."""
        self.update_positions()
        summary = self._get_portfolio_summary()
        
        report = f"""
RISK-MANAGED PORTFOLIO REPORT
{'=' * 50}

Portfolio Summary:
- Total Value: ${summary['total_value']:,.2f}
- Cash: ${summary['cash']:,.2f} ({summary['cash']/summary['total_value']*100:.1f}%)
- Invested: ${summary['invested_value']:,.2f}
- Total Return: {summary['total_return_pct']:.2f}%
- Number of Positions: {summary['number_of_positions']}

Current Positions:
"""
        
        if self.positions:
            for symbol, position in self.positions.items():
                position_value = position.shares * position.current_price
                report += f"- {symbol}: {position.shares} shares @ ${position.current_price:.2f} "
                report += f"(${position_value:,.0f}, {position.unrealized_pnl_pct:+.1f}%)\n"
        else:
            report += "No current positions\n"
        
        report += f"\nSector Allocation:\n"
        for sector, allocation in summary['sector_allocation'].items():
            report += f"- {sector}: {allocation*100:.1f}%\n"
        
        report += f"\nReport generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return report


# Example usage
if __name__ == "__main__":
    print("Risk-managed portfolio module loaded successfully")
    
    # Test initialization
    portfolio = RiskManagedPortfolio(initial_capital=100000)
    print(f"Portfolio initialized with ${portfolio.initial_capital:,.0f}")
    print(f"Max position size: {portfolio.max_position_size*100:.1f}%")
    print(f"Max sector allocation: {portfolio.max_sector_allocation*100:.1f}%")
