"""
Data fetching module for stock trading AI.
Handles downloading and preprocessing of financial data.
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataFetcher:
    """
    Handles fetching and preprocessing of stock market data.
    """
    
    def __init__(self):
        self.cache = {}
    
    def fetch_stock_data(
        self, 
        symbol: str, 
        period: str = "2y", 
        interval: str = "1d"
    ) -> pd.DataFrame:
        """
        Fetch stock data for a given symbol.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL', 'MSFT')
            period: Data period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval: Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')
        
        Returns:
            DataFrame with OHLCV data
        """
        cache_key = f"{symbol}_{period}_{interval}"
        
        if cache_key in self.cache:
            logger.info(f"Using cached data for {symbol}")
            return self.cache[cache_key].copy()
        
        try:
            logger.info(f"Fetching data for {symbol} (period: {period}, interval: {interval})")
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}")
            
            # Clean the data
            data = self._clean_data(data)
            
            # Cache the data
            self.cache[cache_key] = data.copy()
            
            logger.info(f"Successfully fetched {len(data)} rows for {symbol}")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            raise
    
    def fetch_multiple_stocks(
        self, 
        symbols: List[str], 
        period: str = "2y", 
        interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """
        Fetch data for multiple stocks.
        
        Args:
            symbols: List of stock symbols
            period: Data period
            interval: Data interval
        
        Returns:
            Dictionary mapping symbols to their DataFrames
        """
        data_dict = {}
        
        for symbol in symbols:
            try:
                data_dict[symbol] = self.fetch_stock_data(symbol, period, interval)
            except Exception as e:
                logger.warning(f"Failed to fetch data for {symbol}: {str(e)}")
                continue
        
        return data_dict
    
    def get_stock_info(self, symbol: str) -> Dict:
        """
        Get basic information about a stock.
        
        Args:
            symbol: Stock symbol
        
        Returns:
            Dictionary with stock information
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Extract key information
            key_info = {
                'symbol': symbol,
                'company_name': info.get('longName', 'N/A'),
                'sector': info.get('sector', 'N/A'),
                'industry': info.get('industry', 'N/A'),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'dividend_yield': info.get('dividendYield', 0),
                'beta': info.get('beta', 0),
                'current_price': info.get('currentPrice', 0)
            }
            
            return key_info
            
        except Exception as e:
            logger.error(f"Error fetching info for {symbol}: {str(e)}")
            return {'symbol': symbol, 'error': str(e)}
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and preprocess the data.
        
        Args:
            data: Raw OHLCV data
        
        Returns:
            Cleaned DataFrame
        """
        # Remove any rows with NaN values
        data = data.dropna()
        
        # Ensure we have the required columns
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Remove any rows where High < Low (data errors)
        data = data[data['High'] >= data['Low']]
        
        # Remove any rows where Close is outside High/Low range
        data = data[(data['Close'] >= data['Low']) & (data['Close'] <= data['High'])]
        
        # Remove any rows with zero or negative prices
        price_columns = ['Open', 'High', 'Low', 'Close']
        for col in price_columns:
            data = data[data[col] > 0]
        
        # Remove any rows with zero volume (if we want to filter these out)
        # data = data[data['Volume'] > 0]
        
        return data
    
    def get_market_data(self, symbols: List[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Get data for a diversified set of market symbols.
        
        Args:
            symbols: Optional list of symbols. If None, uses default diversified portfolio.
        
        Returns:
            Dictionary mapping symbols to their DataFrames
        """
        if symbols is None:
            # Default diversified portfolio across sectors
            symbols = [
                # Technology
                'AAPL', 'MSFT', 'GOOGL', 'NVDA', 'META',
                # Healthcare
                'JNJ', 'PFE', 'UNH', 'ABBV',
                # Financial Services
                'JPM', 'BAC', 'WFC', 'GS',
                # Consumer Discretionary
                'AMZN', 'TSLA', 'HD', 'MCD',
                # Energy
                'XOM', 'CVX', 'COP',
                # Utilities
                'NEE', 'DUK',
                # Materials
                'LIN', 'APD',
                # Real Estate
                'AMT', 'PLD',
                # ETFs for broader exposure
                'SPY', 'QQQ', 'IWM', 'VTI'
            ]
        
        return self.fetch_multiple_stocks(symbols)
    
    def clear_cache(self):
        """Clear the data cache."""
        self.cache.clear()
        logger.info("Data cache cleared")


# Example usage and testing
if __name__ == "__main__":
    # Test the data fetcher
    fetcher = DataFetcher()
    
    # Test single stock
    try:
        aapl_data = fetcher.fetch_stock_data('AAPL', period='1y')
        print(f"AAPL data shape: {aapl_data.shape}")
        print(f"AAPL data columns: {aapl_data.columns.tolist()}")
        print(f"AAPL data head:\n{aapl_data.head()}")
        
        # Test stock info
        aapl_info = fetcher.get_stock_info('AAPL')
        print(f"AAPL info: {aapl_info}")
        
    except Exception as e:
        print(f"Error testing data fetcher: {e}")
