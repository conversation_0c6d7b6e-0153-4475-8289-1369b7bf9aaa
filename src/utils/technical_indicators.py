"""
Technical indicators module for stock trading AI.
Implements common technical analysis indicators without TA-Lib dependency.
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """
    Collection of technical analysis indicators.
    """
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """
        Simple Moving Average.
        
        Args:
            data: Price series (typically Close prices)
            window: Number of periods for the moving average
        
        Returns:
            Series with SMA values
        """
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """
        Exponential Moving Average.
        
        Args:
            data: Price series (typically Close prices)
            window: Number of periods for the moving average
        
        Returns:
            Series with EMA values
        """
        return data.ewm(span=window, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """
        Relative Strength Index.
        
        Args:
            data: Price series (typically Close prices)
            window: Number of periods for RSI calculation
        
        Returns:
            Series with RSI values (0-100)
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Moving Average Convergence Divergence.
        
        Args:
            data: Price series (typically Close prices)
            fast: Fast EMA period
            slow: Slow EMA period
            signal: Signal line EMA period
        
        Returns:
            Tuple of (MACD line, Signal line, Histogram)
        """
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Bollinger Bands.
        
        Args:
            data: Price series (typically Close prices)
            window: Number of periods for moving average
            num_std: Number of standard deviations for bands
        
        Returns:
            Tuple of (Upper band, Middle band/SMA, Lower band)
        """
        sma = TechnicalIndicators.sma(data, window)
        std = data.rolling(window=window).std()
        
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        
        return upper_band, sma, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_window: int = 14, d_window: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        Stochastic Oscillator.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            k_window: %K period
            d_window: %D period
        
        Returns:
            Tuple of (%K, %D)
        """
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window).mean()
        
        return k_percent, d_percent
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        Average True Range.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            window: Number of periods for ATR calculation
        
        Returns:
            Series with ATR values
        """
        high_low = high - low
        high_close_prev = np.abs(high - close.shift(1))
        low_close_prev = np.abs(low - close.shift(1))
        
        true_range = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
        atr = pd.Series(true_range).rolling(window=window).mean()
        
        return atr
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        Williams %R.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            window: Number of periods for calculation
        
        Returns:
            Series with Williams %R values (-100 to 0)
        """
        highest_high = high.rolling(window=window).max()
        lowest_low = low.rolling(window=window).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return williams_r
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        On-Balance Volume.
        
        Args:
            close: Close price series
            volume: Volume series
        
        Returns:
            Series with OBV values
        """
        price_change = close.diff()
        obv = volume.copy()
        
        obv[price_change < 0] = -volume[price_change < 0]
        obv[price_change == 0] = 0
        
        return obv.cumsum()
    
    @staticmethod
    def add_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        Add all technical indicators to a DataFrame.
        
        Args:
            df: DataFrame with OHLCV data
        
        Returns:
            DataFrame with all technical indicators added
        """
        result = df.copy()
        
        # Moving Averages
        result['SMA_20'] = TechnicalIndicators.sma(df['Close'], 20)
        result['SMA_50'] = TechnicalIndicators.sma(df['Close'], 50)
        result['SMA_200'] = TechnicalIndicators.sma(df['Close'], 200)
        result['EMA_12'] = TechnicalIndicators.ema(df['Close'], 12)
        result['EMA_26'] = TechnicalIndicators.ema(df['Close'], 26)
        
        # RSI
        result['RSI'] = TechnicalIndicators.rsi(df['Close'])
        
        # MACD
        macd_line, signal_line, histogram = TechnicalIndicators.macd(df['Close'])
        result['MACD'] = macd_line
        result['MACD_Signal'] = signal_line
        result['MACD_Histogram'] = histogram
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['Close'])
        result['BB_Upper'] = bb_upper
        result['BB_Middle'] = bb_middle
        result['BB_Lower'] = bb_lower
        result['BB_Width'] = bb_upper - bb_lower
        result['BB_Position'] = (df['Close'] - bb_lower) / (bb_upper - bb_lower)
        
        # Stochastic
        stoch_k, stoch_d = TechnicalIndicators.stochastic(df['High'], df['Low'], df['Close'])
        result['Stoch_K'] = stoch_k
        result['Stoch_D'] = stoch_d
        
        # ATR
        result['ATR'] = TechnicalIndicators.atr(df['High'], df['Low'], df['Close'])
        
        # Williams %R
        result['Williams_R'] = TechnicalIndicators.williams_r(df['High'], df['Low'], df['Close'])
        
        # OBV
        result['OBV'] = TechnicalIndicators.obv(df['Close'], df['Volume'])
        
        # Price-based indicators
        result['Price_Change'] = df['Close'].pct_change()
        result['Price_Change_5d'] = df['Close'].pct_change(5)
        result['Volatility_20d'] = df['Close'].pct_change().rolling(20).std()
        
        # Volume indicators
        result['Volume_SMA_20'] = TechnicalIndicators.sma(df['Volume'], 20)
        result['Volume_Ratio'] = df['Volume'] / result['Volume_SMA_20']
        
        return result


# Example usage and testing
if __name__ == "__main__":
    # Create sample data for testing
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # Generate sample OHLCV data
    close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
    high_prices = close_prices + np.random.rand(100) * 2
    low_prices = close_prices - np.random.rand(100) * 2
    open_prices = close_prices + np.random.randn(100) * 0.5
    volumes = np.random.randint(1000000, 10000000, 100)
    
    sample_data = pd.DataFrame({
        'Open': open_prices,
        'High': high_prices,
        'Low': low_prices,
        'Close': close_prices,
        'Volume': volumes
    }, index=dates)
    
    # Test indicators
    indicators = TechnicalIndicators()
    
    # Test individual indicators
    sma_20 = indicators.sma(sample_data['Close'], 20)
    rsi = indicators.rsi(sample_data['Close'])
    macd_line, signal_line, histogram = indicators.macd(sample_data['Close'])
    
    print("Sample technical indicators:")
    print(f"SMA(20) last 5 values: {sma_20.tail().values}")
    print(f"RSI last 5 values: {rsi.tail().values}")
    print(f"MACD last 5 values: {macd_line.tail().values}")
    
    # Test adding all indicators
    data_with_indicators = indicators.add_all_indicators(sample_data)
    print(f"\nDataFrame shape after adding indicators: {data_with_indicators.shape}")
    print(f"New columns: {[col for col in data_with_indicators.columns if col not in sample_data.columns]}")
