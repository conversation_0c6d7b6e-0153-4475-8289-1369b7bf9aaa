#!/usr/bin/env python3
"""
Complete Trading AI System Demonstration.
Shows the full pipeline from data fetching to risk-managed trading.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from src.trading_ai import TradingAI
from src.ml_models.ml_strategy import MLTradingStrategy
from src.risk_management.portfolio_manager import RiskManagedPortfolio
from src.backtesting.backtrader_engine import BacktestEngine


class CompleteTradingSystem:
    """
    Complete trading system that integrates all components.
    """
    
    def __init__(self, initial_capital: float = 100000):
        """Initialize the complete trading system."""
        self.initial_capital = initial_capital
        
        # Initialize components
        self.basic_ai = TradingAI()
        self.ml_strategy = MLTradingStrategy(model_type='ensemble')
        self.portfolio = RiskManagedPortfolio(initial_capital=initial_capital)
        self.backtest_engine = BacktestEngine(initial_capital=initial_capital)
        
        # Test universe - diversified across sectors
        self.test_universe = [
            # Technology
            'AAPL', 'MSFT', 'GOOGL', 'NVDA',
            # Healthcare  
            'JNJ', 'PFE',
            # Financial
            'JPM', 'BAC',
            # Consumer
            'PG', 'KO',
            # Energy
            'XOM', 'CVX',
            # Industrials
            'CAT', 'BA'
        ]
        
        print("Complete Trading System initialized")
        print(f"Initial Capital: ${initial_capital:,.0f}")
        print(f"Test Universe: {len(self.test_universe)} stocks")
    
    def run_complete_demo(self):
        """Run the complete system demonstration."""
        print("\n" + "="*80)
        print("COMPLETE TRADING AI SYSTEM DEMONSTRATION")
        print("="*80)
        
        # Step 1: Basic Technical Analysis
        print("\n1. BASIC TECHNICAL ANALYSIS")
        print("-" * 40)
        self.demo_basic_analysis()
        
        # Step 2: Machine Learning Enhancement
        print("\n2. MACHINE LEARNING ENHANCEMENT")
        print("-" * 40)
        self.demo_ml_enhancement()
        
        # Step 3: Risk-Managed Portfolio
        print("\n3. RISK-MANAGED PORTFOLIO")
        print("-" * 40)
        self.demo_risk_management()
        
        # Step 4: Comprehensive Backtesting
        print("\n4. COMPREHENSIVE BACKTESTING")
        print("-" * 40)
        self.demo_backtesting()
        
        # Step 5: Performance Summary
        print("\n5. PERFORMANCE SUMMARY")
        print("-" * 40)
        self.generate_summary()
    
    def demo_basic_analysis(self):
        """Demonstrate basic technical analysis."""
        try:
            # Analyze a sample stock
            sample_stock = 'AAPL'
            print(f"Analyzing {sample_stock} with basic technical indicators...")
            
            result = self.basic_ai.analyze_stock(sample_stock)
            
            if 'error' not in result:
                current = result['current_analysis']
                print(f"✓ Signal: {current['current_signal']} (Strength: {current['signal_strength']:.2f})")
                print(f"✓ Current Price: ${current['current_price']:.2f}")
                print(f"✓ RSI: {current['rsi']:.1f}")
                print(f"✓ Trend: {current['ema_trend']}")
                
                # Test portfolio analysis
                print(f"\nAnalyzing portfolio of {len(self.test_universe[:5])} stocks...")
                portfolio_result = self.basic_ai.analyze_portfolio(self.test_universe[:5])
                
                print(f"✓ Buy recommendations: {len(portfolio_result['buy_recommendations'])}")
                print(f"✓ Sell recommendations: {len(portfolio_result['sell_recommendations'])}")
                print(f"✓ Hold recommendations: {len(portfolio_result['hold_recommendations'])}")
                
                return True
            else:
                print(f"✗ Error: {result['error']}")
                return False
                
        except Exception as e:
            print(f"✗ Error in basic analysis: {e}")
            return False
    
    def demo_ml_enhancement(self):
        """Demonstrate machine learning enhancement."""
        try:
            print("Training ML models on historical data...")
            
            # Get training data
            sample_stock = 'AAPL'
            training_data = self.basic_ai.data_fetcher.fetch_stock_data(sample_stock, period='2y')
            
            print(f"✓ Training data: {len(training_data)} days")
            
            # Train ML models
            training_results = self.ml_strategy.train_models(training_data)
            
            if 'error' not in training_results:
                print("✓ ML models trained successfully")
                
                # Show training metrics
                for model_name, metrics in training_results.items():
                    if isinstance(metrics, dict) and 'accuracy' in metrics:
                        print(f"  {model_name}: Accuracy {metrics['accuracy']:.3f}, F1 {metrics['f1']:.3f}")
                
                # Test predictions
                print("\nTesting ML predictions...")
                test_data = self.basic_ai.data_fetcher.fetch_stock_data(sample_stock, period='3mo')
                predictions = self.ml_strategy.predict_signals(test_data)
                
                latest_ml_signal = predictions['ML_Signal'].iloc[-1]
                latest_confidence = predictions['ML_Confidence'].iloc[-1]
                
                signal_map = {1: 'BUY', -1: 'SELL', 0: 'HOLD'}
                print(f"✓ Latest ML Signal: {signal_map.get(latest_ml_signal, 'UNKNOWN')} (Confidence: {latest_confidence:.2f})")
                
                return True
            else:
                print(f"✗ ML training error: {training_results['error']}")
                return False
                
        except Exception as e:
            print(f"✗ Error in ML enhancement: {e}")
            return False
    
    def demo_risk_management(self):
        """Demonstrate risk-managed portfolio."""
        try:
            # Add ML strategy to portfolio if trained
            if self.ml_strategy.is_trained:
                self.portfolio.add_ml_strategy(self.ml_strategy)
                print("✓ ML strategy integrated with portfolio")
            
            # Analyze universe
            print(f"Analyzing universe of {len(self.test_universe[:8])} stocks...")
            recommendations = self.portfolio.analyze_universe(self.test_universe[:8])
            
            print(f"✓ Buy candidates: {len(recommendations['buy_candidates'])}")
            print(f"✓ Sell candidates: {len(recommendations['sell_candidates'])}")
            print(f"✓ Hold positions: {len(recommendations['hold_positions'])}")
            
            # Show top recommendations
            if recommendations['buy_candidates']:
                print("\nTop Buy Recommendations:")
                for i, candidate in enumerate(recommendations['buy_candidates'][:3], 1):
                    print(f"  {i}. {candidate['symbol']} - {candidate['sector']} "
                          f"(Signal: {candidate['signal_strength']:.2f}, Vol: {candidate['volatility']:.2f})")
            
            # Risk analysis
            risk_analysis = recommendations['risk_analysis']
            print(f"\nRisk Analysis:")
            print(f"✓ Portfolio Value: ${risk_analysis['total_portfolio_value']:,.0f}")
            print(f"✓ Cash: {risk_analysis['cash_percentage']:.1f}%")
            print(f"✓ Positions: {risk_analysis['number_of_positions']}")
            print(f"✓ Max Sector Concentration: {risk_analysis['max_sector_concentration']*100:.1f}%")
            
            # Simulate some trades (for demo purposes)
            if recommendations['buy_candidates']:
                print("\nSimulating trades...")
                execution_results = self.portfolio.execute_trades(recommendations)
                print(f"✓ Executed {len(execution_results['executed_buys'])} buys")
                print(f"✓ Executed {len(execution_results['executed_sells'])} sells")
                print(f"✓ Rejected {len(execution_results['rejected_trades'])} trades")
            
            return True
            
        except Exception as e:
            print(f"✗ Error in risk management demo: {e}")
            return False
    
    def demo_backtesting(self):
        """Demonstrate comprehensive backtesting."""
        try:
            print("Running backtest on sample stock...")
            
            # Get test data
            sample_stock = 'MSFT'
            test_data = self.basic_ai.data_fetcher.fetch_stock_data(sample_stock, period='1y')
            
            print(f"✓ Test data: {len(test_data)} days")
            
            # Run backtest with different strategy variants
            strategy_variants = [
                # Conservative
                {'risk_per_trade': 0.01, 'stop_loss_pct': 0.03},
                # Balanced  
                {'risk_per_trade': 0.02, 'stop_loss_pct': 0.05},
                # Aggressive
                {'risk_per_trade': 0.03, 'stop_loss_pct': 0.07}
            ]
            
            backtest_results = {}
            
            for i, params in enumerate(strategy_variants, 1):
                print(f"  Testing strategy variant {i}...")
                result = self.backtest_engine.run_backtest(test_data, params)
                backtest_results[f'variant_{i}'] = result
                
                print(f"    Return: {result['total_return_pct']:.2f}%, "
                      f"Sharpe: {result['sharpe_ratio']:.2f}, "
                      f"Max DD: {result['max_drawdown_pct']:.2f}%")
            
            # Find best performing variant
            best_variant = max(backtest_results.items(), 
                             key=lambda x: x[1]['total_return_pct'])
            
            print(f"\n✓ Best performing variant: {best_variant[0]}")
            print(f"  Return: {best_variant[1]['total_return_pct']:.2f}%")
            print(f"  Sharpe Ratio: {best_variant[1]['sharpe_ratio']:.2f}")
            print(f"  Win Rate: {best_variant[1]['win_rate']:.1f}%")
            
            return backtest_results
            
        except Exception as e:
            print(f"✗ Error in backtesting demo: {e}")
            return {}
    
    def generate_summary(self):
        """Generate final performance summary."""
        try:
            print("Generating comprehensive performance summary...")
            
            # Portfolio summary
            portfolio_summary = self.portfolio._get_portfolio_summary()
            
            print(f"\nFINAL PORTFOLIO STATUS:")
            print(f"✓ Total Value: ${portfolio_summary['total_value']:,.2f}")
            print(f"✓ Total Return: {portfolio_summary['total_return_pct']:.2f}%")
            print(f"✓ Cash Position: ${portfolio_summary['cash']:,.2f}")
            print(f"✓ Active Positions: {portfolio_summary['number_of_positions']}")
            
            # Risk metrics
            if portfolio_summary['sector_allocation']:
                print(f"\nSECTOR DIVERSIFICATION:")
                for sector, allocation in portfolio_summary['sector_allocation'].items():
                    print(f"  {sector}: {allocation*100:.1f}%")
            
            # ML model performance (if available)
            if self.ml_strategy.is_trained and self.ml_strategy.training_metrics:
                print(f"\nML MODEL PERFORMANCE:")
                for model_name, metrics in self.ml_strategy.training_metrics.items():
                    if isinstance(metrics, dict) and 'accuracy' in metrics:
                        print(f"  {model_name}: {metrics['accuracy']:.3f} accuracy")
            
            # Feature importance (if available)
            if self.ml_strategy.is_trained:
                feature_importance = self.ml_strategy.get_feature_importance(top_n=5)
                if feature_importance:
                    print(f"\nTOP PREDICTIVE FEATURES:")
                    for model_name, features in feature_importance.items():
                        print(f"  {model_name}:")
                        for feature, importance in list(features.items())[:3]:
                            print(f"    {feature}: {importance:.3f}")
            
            print(f"\n{'='*80}")
            print("DEMONSTRATION COMPLETED SUCCESSFULLY")
            print(f"{'='*80}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error generating summary: {e}")
            return False
    
    def save_results(self):
        """Save all results and models."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Save ML models if trained
            if self.ml_strategy.is_trained:
                model_path = f'ml_models_{timestamp}.joblib'
                self.ml_strategy.save_models(model_path)
                print(f"✓ ML models saved to {model_path}")
            
            # Save portfolio report
            portfolio_report = self.portfolio.generate_report()
            report_path = f'portfolio_report_{timestamp}.txt'
            with open(report_path, 'w') as f:
                f.write(portfolio_report)
            print(f"✓ Portfolio report saved to {report_path}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error saving results: {e}")
            return False


def main():
    """Main function to run the complete system demo."""
    print("COMPLETE TRADING AI SYSTEM")
    print("=" * 50)
    print("This demonstration shows the full pipeline:")
    print("1. Technical Analysis")
    print("2. Machine Learning Enhancement") 
    print("3. Risk Management")
    print("4. Backtesting")
    print("5. Performance Analysis")
    print()
    
    # Initialize system
    system = CompleteTradingSystem(initial_capital=100000)
    
    # Run complete demonstration
    system.run_complete_demo()
    
    # Save results
    print("\nSaving results...")
    system.save_results()
    
    print("\n🎉 Complete system demonstration finished!")
    print("\nKey Features Demonstrated:")
    print("✓ Multi-indicator technical analysis")
    print("✓ Machine learning signal enhancement")
    print("✓ Risk-managed portfolio allocation")
    print("✓ Sector diversification")
    print("✓ Comprehensive backtesting")
    print("✓ Performance metrics and reporting")


if __name__ == "__main__":
    main()
