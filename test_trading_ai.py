#!/usr/bin/env python3
"""
Test script for the Trading AI system.
Demonstrates the functionality of the trading AI with real stock data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading_ai import TradingAI
from src.data.data_fetcher import DataFetcher
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


def test_single_stock_analysis():
    """Test analysis of a single stock."""
    print("=" * 60)
    print("TESTING SINGLE STOCK ANALYSIS")
    print("=" * 60)
    
    # Initialize the AI
    ai = TradingAI()
    
    # Test with Apple stock
    symbol = 'AAPL'
    print(f"Analyzing {symbol}...")
    
    try:
        result = ai.analyze_stock(symbol)
        
        if 'error' in result:
            print(f"Error: {result['error']}")
            return
        
        # Print the report
        print(ai.generate_report(symbol))
        
        # Show some key metrics
        current = result['current_analysis']
        backtest = result['backtest_results']
        
        print(f"\nKey Insights for {symbol}:")
        print(f"- Current Signal: {current['current_signal']} (Strength: {current['signal_strength']:.2f})")
        print(f"- Backtest Return: {backtest['total_return']:.2f}%")
        print(f"- Current RSI: {current['rsi']:.1f}")
        print(f"- Trend Direction: {current['ema_trend']}")
        
        return result
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        return None


def test_portfolio_analysis():
    """Test analysis of a portfolio of stocks."""
    print("\n" + "=" * 60)
    print("TESTING PORTFOLIO ANALYSIS")
    print("=" * 60)
    
    # Initialize the AI
    ai = TradingAI()
    
    # Define a test portfolio with stocks from different sectors
    test_portfolio = [
        'AAPL',  # Technology
        'MSFT',  # Technology
        'JNJ',   # Healthcare
        'JPM',   # Financial
        'XOM',   # Energy
        'PG',    # Consumer Goods
        'DIS',   # Entertainment
        'NVDA',  # Technology/AI
        'TSLA',  # Automotive/Technology
        'KO'     # Consumer Staples
    ]
    
    print(f"Analyzing portfolio with {len(test_portfolio)} stocks...")
    print(f"Stocks: {', '.join(test_portfolio)}")
    
    try:
        portfolio_result = ai.analyze_portfolio(test_portfolio)
        
        # Print portfolio report
        print(ai.generate_report())
        
        # Show top recommendations
        print("\n" + "-" * 40)
        print("TOP BUY RECOMMENDATIONS:")
        print("-" * 40)
        
        top_buys = ai.get_top_recommendations('BUY', 3)
        for i, rec in enumerate(top_buys, 1):
            print(f"{i}. {rec['symbol']}: {rec['reason']}")
            print(f"   Price: ${rec['current_price']:.2f}, Strength: {rec['strength']:.2f}")
            print(f"   Backtest Return: {rec['backtest_return']:.2f}%")
        
        print("\n" + "-" * 40)
        print("TOP SELL RECOMMENDATIONS:")
        print("-" * 40)
        
        top_sells = ai.get_top_recommendations('SELL', 3)
        for i, rec in enumerate(top_sells, 1):
            print(f"{i}. {rec['symbol']}: {rec['reason']}")
            print(f"   Price: ${rec['current_price']:.2f}, Strength: {rec['strength']:.2f}")
            print(f"   Backtest Return: {rec['backtest_return']:.2f}%")
        
        return portfolio_result
        
    except Exception as e:
        print(f"Error during portfolio analysis: {e}")
        return None


def test_data_fetcher():
    """Test the data fetching functionality."""
    print("\n" + "=" * 60)
    print("TESTING DATA FETCHER")
    print("=" * 60)
    
    fetcher = DataFetcher()
    
    # Test single stock data fetch
    try:
        print("Fetching AAPL data...")
        aapl_data = fetcher.fetch_stock_data('AAPL', period='1y')
        print(f"✓ Successfully fetched {len(aapl_data)} days of data")
        print(f"  Date range: {aapl_data.index[0].date()} to {aapl_data.index[-1].date()}")
        print(f"  Columns: {list(aapl_data.columns)}")
        print(f"  Latest close price: ${aapl_data['Close'].iloc[-1]:.2f}")
        
        # Test stock info
        print("\nFetching AAPL company info...")
        aapl_info = fetcher.get_stock_info('AAPL')
        print(f"✓ Company: {aapl_info.get('company_name', 'N/A')}")
        print(f"  Sector: {aapl_info.get('sector', 'N/A')}")
        print(f"  Market Cap: ${aapl_info.get('market_cap', 0):,.0f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing data fetcher: {e}")
        return False


def create_visualization(analysis_result):
    """Create a simple visualization of the analysis."""
    if not analysis_result or 'data_with_signals' not in analysis_result:
        print("No data available for visualization")
        return
    
    print("\n" + "=" * 60)
    print("CREATING VISUALIZATION")
    print("=" * 60)
    
    try:
        data = analysis_result['data_with_signals']
        symbol = analysis_result['symbol']
        
        # Create a simple plot
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{symbol} Technical Analysis', fontsize=16)
        
        # Price and moving averages
        axes[0, 0].plot(data.index, data['Close'], label='Close Price', linewidth=2)
        axes[0, 0].plot(data.index, data['EMA_Fast'], label='EMA Fast', alpha=0.7)
        axes[0, 0].plot(data.index, data['EMA_Slow'], label='EMA Slow', alpha=0.7)
        axes[0, 0].set_title('Price and EMAs')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # RSI
        axes[0, 1].plot(data.index, data['RSI'], label='RSI', color='purple')
        axes[0, 1].axhline(y=70, color='r', linestyle='--', alpha=0.7, label='Overbought')
        axes[0, 1].axhline(y=30, color='g', linestyle='--', alpha=0.7, label='Oversold')
        axes[0, 1].set_title('RSI')
        axes[0, 1].set_ylim(0, 100)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # MACD
        axes[1, 0].plot(data.index, data['MACD'], label='MACD', color='blue')
        axes[1, 0].plot(data.index, data['MACD_Signal'], label='Signal', color='red')
        axes[1, 0].bar(data.index, data['MACD_Histogram'], label='Histogram', alpha=0.3)
        axes[1, 0].set_title('MACD')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Bollinger Bands
        axes[1, 1].plot(data.index, data['Close'], label='Close Price', linewidth=2)
        axes[1, 1].plot(data.index, data['BB_Upper'], label='BB Upper', alpha=0.7)
        axes[1, 1].plot(data.index, data['BB_Lower'], label='BB Lower', alpha=0.7)
        axes[1, 1].fill_between(data.index, data['BB_Upper'], data['BB_Lower'], alpha=0.1)
        axes[1, 1].set_title('Bollinger Bands')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{symbol}_analysis.png', dpi=300, bbox_inches='tight')
        print(f"✓ Visualization saved as {symbol}_analysis.png")
        
        # Don't show the plot in automated testing
        # plt.show()
        
    except Exception as e:
        print(f"✗ Error creating visualization: {e}")


def main():
    """Main test function."""
    print("TRADING AI SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: Data Fetcher
    data_test_passed = test_data_fetcher()
    
    if not data_test_passed:
        print("Data fetcher test failed. Stopping tests.")
        return
    
    # Test 2: Single Stock Analysis
    single_stock_result = test_single_stock_analysis()
    
    # Test 3: Portfolio Analysis
    portfolio_result = test_portfolio_analysis()
    
    # Test 4: Create Visualization (if single stock analysis succeeded)
    if single_stock_result:
        create_visualization(single_stock_result)
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"✓ Data Fetcher: {'PASSED' if data_test_passed else 'FAILED'}")
    print(f"✓ Single Stock Analysis: {'PASSED' if single_stock_result else 'FAILED'}")
    print(f"✓ Portfolio Analysis: {'PASSED' if portfolio_result else 'FAILED'}")
    print(f"✓ Visualization: {'PASSED' if single_stock_result else 'SKIPPED'}")
    
    if all([data_test_passed, single_stock_result, portfolio_result]):
        print("\n🎉 All tests PASSED! Trading AI is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
